/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import {Injectable} from '@nestjs/common';
import {ConfigService} from "@nestjs/config";
import * as newAgora from "agora-token" ;
import {RtcTokenBuilder} from "agora-token";
import { LiveStreamParticipantRole } from '../../core/utils/enums';

@Injectable()
export class AgoraService {
    constructor(
        private readonly configService: ConfigService,
    ) {
    }

    getAgoraAccessNew(channelName: string, create: boolean) {
        let role = newAgora.RtcRole.PUBLISHER;
        let expireTime = 3600
        let currentTime = Math.floor(Date.now() / 1000);
        let privilegeExpireTime = currentTime + expireTime;
        let token = RtcTokenBuilder.buildTokenWithUid(
            this.configService.getOrThrow("AGORA_APP_ID"),
            this.configService.getOrThrow("AGORA_APP_CERTIFICATE"),
            channelName,
            0,
            role,
            expireTime,
            privilegeExpireTime,
        );
        return ({
            'channelName': channelName,
            'uid': 0,
            'rtcToken': token,
            'joinedAt': new Date()
        });
    }

    getAgoraAccess(channelName: string, userId: string, create: boolean) {
        return this.getAgoraAccessNew(channelName, create)
    }

    /**
     * Get Agora access token for live streaming
     * @param channelName - The live stream channel name
     * @param userId - User ID
     * @param role - Host or Viewer role
     * @param expireTime - Token expiration time in seconds (default: 3600)
     */
    getLiveStreamAgoraAccess(
        channelName: string,
        userId: string,
        role: LiveStreamParticipantRole,
        expireTime: number = 3600
    ) {
        // Set Agora role based on live stream role
        let agoraRole = role === LiveStreamParticipantRole.Host
            ? newAgora.RtcRole.PUBLISHER
            : newAgora.RtcRole.SUBSCRIBER;

        let currentTime = Math.floor(Date.now() / 1000);
        let privilegeExpireTime = currentTime + expireTime;

        // Generate unique UID for the user (use hash of userId for consistency)
        let uid = this.generateUidFromUserId(userId);

        let token = RtcTokenBuilder.buildTokenWithUid(
            this.configService.getOrThrow("AGORA_APP_ID"),
            this.configService.getOrThrow("AGORA_APP_CERTIFICATE"),
            channelName,
            uid,
            agoraRole,
            expireTime,
            privilegeExpireTime,
        );

        return {
            channelName: channelName,
            uid: uid,
            rtcToken: token,
            role: role,
            agoraRole: agoraRole,
            joinedAt: new Date(),
            expiresAt: new Date(privilegeExpireTime * 1000)
        };
    }

    /**
     * Generate a consistent UID from user ID
     * Agora UIDs should be 32-bit unsigned integers
     */
    private generateUidFromUserId(userId: string): number {
        // Simple hash function to convert string to number
        let hash = 0;
        for (let i = 0; i < userId.length; i++) {
            const char = userId.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        // Ensure positive number and within 32-bit range
        return Math.abs(hash) % 2147483647;
    }

    /**
     * Get Agora access for live stream host
     */
    getLiveStreamHostAccess(channelName: string, hostId: string) {
        return this.getLiveStreamAgoraAccess(
            channelName,
            hostId,
            LiveStreamParticipantRole.Host,
            7200 // 2 hours for host
        );
    }

    /**
     * Get Agora access for live stream viewer
     */
    getLiveStreamViewerAccess(channelName: string, viewerId: string) {
        return this.getLiveStreamAgoraAccess(
            channelName,
            viewerId,
            LiveStreamParticipantRole.Viewer,
            3600 // 1 hour for viewer
        );
    }

    /**
     * Refresh Agora token for live streaming
     */
    refreshLiveStreamToken(
        channelName: string,
        userId: string,
        role: LiveStreamParticipantRole
    ) {
        return this.getLiveStreamAgoraAccess(channelName, userId, role);
    }
}
