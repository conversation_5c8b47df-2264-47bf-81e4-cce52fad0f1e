/**
 * Copyright 2023, the hate<PERSON>ragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, QueryOptions, PaginateModel } from 'mongoose';
import { ILiveStreamParticipant } from '../entities/live_stream_participant.entity';
import { BaseService } from '../../../core/base/base.service';
import { LiveStreamParticipantRole } from '../../../core/utils/enums';
import { isValidMongoId } from '../../../core/utils/app.validator';

@Injectable()
export class LiveStreamParticipantService extends BaseService<ILiveStreamParticipant> {
    constructor(
        @InjectModel('live_stream_participant') private readonly model: PaginateModel<ILiveStreamParticipant>,
    ) {
        super();
    }

    async create(data: Partial<ILiveStreamParticipant>): Promise<ILiveStreamParticipant> {
        const created = await this.model.create(data);
        return this.findById(created._id.toString());
    }

    async findById(id: string, select?: string): Promise<ILiveStreamParticipant | null> {
        if (!isValidMongoId(id)) {
            throw new BadRequestException("Invalid MongoDB ObjectId: " + id);
        }
        return this.model.findById(id, select).populate('userId', 'fullName userImage').lean();
    }

    async findByIdOrThrow(id: string, select?: string): Promise<ILiveStreamParticipant> {
        const participant = await this.findById(id, select);
        if (!participant) {
            throw new NotFoundException("Participant with id " + id + " not found");
        }
        return participant;
    }

    async findOne(filter: FilterQuery<ILiveStreamParticipant>, select?: string): Promise<ILiveStreamParticipant | null> {
        return this.model.findOne(filter, select).populate('userId', 'fullName userImage').lean();
    }

    async findAll(
        filter?: FilterQuery<ILiveStreamParticipant>,
        select?: string,
        options?: QueryOptions<ILiveStreamParticipant>
    ): Promise<ILiveStreamParticipant[]> {
        return this.model.find(filter, select, options).populate('userId', 'fullName userImage').lean();
    }

    async updateById(id: string, update: Partial<ILiveStreamParticipant>): Promise<ILiveStreamParticipant | null> {
        return this.model.findByIdAndUpdate(id, update, { new: true }).populate('userId', 'fullName userImage').lean();
    }

    async deleteById(id: string): Promise<any> {
        return this.model.findByIdAndDelete(id);
    }

    async joinLiveStream(liveStreamId: string, userId: string, role: LiveStreamParticipantRole = LiveStreamParticipantRole.Viewer): Promise<ILiveStreamParticipant> {
        // Check if user is already a participant
        const existingParticipant = await this.findOne({
            liveStreamId,
            userId,
            leftAt: { $exists: false }
        });

        if (existingParticipant) {
            // Update last active time
            return this.updateById(existingParticipant._id, {
                lastActiveAt: new Date()
            });
        }

        // Create new participant
        return this.create({
            liveStreamId,
            userId,
            role,
            joinedAt: new Date(),
            lastActiveAt: new Date()
        });
    }

    async leaveLiveStream(liveStreamId: string, userId: string): Promise<ILiveStreamParticipant | null> {
        const participant = await this.findOne({
            liveStreamId,
            userId,
            leftAt: { $exists: false }
        });

        if (!participant) {
            return null;
        }

        const now = new Date();
        const watchDuration = Math.floor((now.getTime() - participant.joinedAt.getTime()) / 1000);

        return this.updateById(participant._id, {
            leftAt: now,
            watchDuration
        });
    }

    async getActiveParticipants(liveStreamId: string, page: number = 1, limit: number = 50) {
        const filter = {
            liveStreamId,
            leftAt: { $exists: false },
            isBanned: false
        };

        const options = {
            page,
            limit,
            sort: { joinedAt: 1 },
            populate: [
                { path: 'userId', select: 'fullName userImage' }
            ]
        };

        return this.model.paginate(filter, options);
    }

    async getParticipantCount(liveStreamId: string): Promise<number> {
        return this.model.countDocuments({
            liveStreamId,
            leftAt: { $exists: false },
            isBanned: false
        });
    }

    async banParticipant(liveStreamId: string, userId: string): Promise<ILiveStreamParticipant | null> {
        const participant = await this.findOne({
            liveStreamId,
            userId,
            leftAt: { $exists: false }
        });

        if (!participant) {
            return null;
        }

        return this.updateById(participant._id, {
            isBanned: true,
            leftAt: new Date()
        });
    }

    async unbanParticipant(liveStreamId: string, userId: string): Promise<void> {
        await this.model.updateMany(
            { liveStreamId, userId },
            { isBanned: false }
        );
    }

    async muteParticipant(liveStreamId: string, userId: string): Promise<ILiveStreamParticipant | null> {
        const participant = await this.findOne({
            liveStreamId,
            userId,
            leftAt: { $exists: false }
        });

        if (!participant) {
            return null;
        }

        return this.updateById(participant._id, {
            isMuted: !participant.isMuted
        });
    }

    async updateLastActive(liveStreamId: string, userId: string): Promise<void> {
        await this.model.updateOne(
            {
                liveStreamId,
                userId,
                leftAt: { $exists: false }
            },
            {
                lastActiveAt: new Date()
            }
        );
    }

    async getHostParticipant(liveStreamId: string): Promise<ILiveStreamParticipant | null> {
        return this.findOne({
            liveStreamId,
            role: LiveStreamParticipantRole.Host
        });
    }

    async isUserParticipant(liveStreamId: string, userId: string): Promise<boolean> {
        const participant = await this.findOne({
            liveStreamId,
            userId,
            leftAt: { $exists: false },
            isBanned: false
        });
        return !!participant;
    }
}
