/**
 * Copyright 2023, the hate<PERSON>ragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, QueryOptions, PaginateModel, UpdateQuery } from 'mongoose';
import { ILiveStream } from '../entities/live_stream.entity';
import { BaseService } from '../../../core/common/base.service';
import { LiveStreamStatus, LiveStreamPrivacy } from '../../../core/utils/enums';
import { CreateLiveStreamDto } from '../dto/create-live-stream.dto';
import { v4 as uuidv4 } from 'uuid';
import { isValidMongoId } from '../../../core/utils/utils';

@Injectable()
export class LiveStreamService extends BaseService<ILiveStream> {
    constructor(
        @InjectModel('live_stream') private readonly model: PaginateModel<ILiveStream>,
    ) {
        super();
    }

    // Implement missing abstract methods from BaseService
    async createMany(obj: Partial<ILiveStream>[], session?: any): Promise<any> {
        return this.model.create(obj, { session });
    }

    async findOneAndUpdate(filter: FilterQuery<ILiveStream>, update: UpdateQuery<ILiveStream>, session?: any, options?: QueryOptions<ILiveStream>): Promise<ILiveStream | null> {
        return this.model.findOneAndUpdate(filter, update, { ...options, session, new: true }).lean();
    }

    async findByIdAndUpdate(id: string, update: UpdateQuery<ILiveStream>, session?: any): Promise<ILiveStream | null> {
        if (!isValidMongoId(id)) {
            throw new BadRequestException("Invalid MongoDB ObjectId: " + id);
        }
        return this.model.findByIdAndUpdate(id, update, { session, new: true }).lean();
    }

    async updateMany(filter: FilterQuery<ILiveStream>, update: UpdateQuery<ILiveStream>, session?: any, options?: QueryOptions<ILiveStream>): Promise<any> {
        return this.model.updateMany(filter, update, { ...options, session });
    }

    async findByIdAndDelete(id: string, session?: any): Promise<ILiveStream | null> {
        if (!isValidMongoId(id)) {
            throw new BadRequestException("Invalid MongoDB ObjectId: " + id);
        }
        return this.model.findByIdAndDelete(id, { session }).lean();
    }

    async deleteMany(filter: FilterQuery<ILiveStream>, session?: any): Promise<any> {
        return this.model.deleteMany(filter, { session });
    }

    async findCount(filter: FilterQuery<ILiveStream>, session?: any): Promise<number> {
        return this.model.countDocuments(filter, { session });
    }

    async create(dto: CreateLiveStreamDto): Promise<ILiveStream> {
        // Generate unique Agora channel name
        const agoraChannelName = `live_${dto.myUser._id}_${Date.now()}_${uuidv4().substring(0, 8)}`;
        
        const liveStreamData = {
            hostId: dto.myUser._id,
            title: dto.title,
            description: dto.description,
            privacy: dto.privacy,
            allowedUsers: dto.allowedUsers || [],
            agoraChannelName,
            filters: dto.filters,
            status: LiveStreamStatus.Active,
            startTime: new Date(),
        };

        const created = await this.model.create(liveStreamData);
        return this.findById(created._id.toString());
    }

    async findById(id: string, select?: string): Promise<ILiveStream | null> {
        if (!isValidMongoId(id)) {
            throw new BadRequestException("Invalid MongoDB ObjectId: " + id);
        }
        return this.model.findById(id, select).populate('hostId', 'fullName userImage').lean();
    }

    async findByIdOrThrow(id: string, select?: string): Promise<ILiveStream> {
        const liveStream = await this.findById(id, select);
        if (!liveStream) {
            throw new NotFoundException("Live stream with id " + id + " not found");
        }
        return liveStream;
    }

    async findAll(
        filter?: FilterQuery<ILiveStream>,
        select?: string,
        options?: QueryOptions<ILiveStream>
    ): Promise<ILiveStream[]> {
        return this.model.find(filter, select, options).populate('hostId', 'fullName userImage').lean();
    }

    async findOne(filter: FilterQuery<ILiveStream>, select?: string): Promise<ILiveStream | null> {
        return this.model.findOne(filter, select).populate('hostId', 'fullName userImage').lean();
    }

    async updateById(id: string, update: Partial<ILiveStream>): Promise<ILiveStream | null> {
        return this.model.findByIdAndUpdate(id, update, { new: true }).populate('hostId', 'fullName userImage').lean();
    }

    async deleteById(id: string): Promise<any> {
        return this.model.findByIdAndDelete(id);
    }

    async getActiveLiveStreams(userId: string, page: number = 1, limit: number = 20) {
        const filter: FilterQuery<ILiveStream> = {
            status: LiveStreamStatus.Active,
            $or: [
                { privacy: LiveStreamPrivacy.Public },
                { 
                    privacy: LiveStreamPrivacy.SpecificUsers,
                    allowedUsers: { $in: [userId] }
                }
            ],
            bannedUsers: { $nin: [userId] }
        };

        const options = {
            page,
            limit,
            sort: { startTime: -1 },
            populate: [
                { path: 'hostId', select: 'fullName userImage' }
            ]
        };

        return this.model.paginate(filter, options);
    }

    async endLiveStream(liveStreamId: string, hostId: string): Promise<ILiveStream> {
        const liveStream = await this.findByIdOrThrow(liveStreamId);
        
        if (liveStream.hostId.toString() !== hostId) {
            throw new ForbiddenException("Only the host can end the live stream");
        }

        if (liveStream.status !== LiveStreamStatus.Active) {
            throw new BadRequestException("Live stream is not active");
        }

        return this.updateById(liveStreamId, {
            status: LiveStreamStatus.Ended,
            endTime: new Date()
        });
    }

    async updateViewerCount(liveStreamId: string, increment: number): Promise<void> {
        await this.model.findByIdAndUpdate(liveStreamId, {
            $inc: { 
                viewerCount: increment,
                maxViewers: increment > 0 ? increment : 0
            }
        });
    }

    async incrementCommentCount(liveStreamId: string): Promise<void> {
        await this.model.findByIdAndUpdate(liveStreamId, {
            $inc: { totalComments: 1 }
        });
    }

    async incrementReactionCount(liveStreamId: string): Promise<void> {
        await this.model.findByIdAndUpdate(liveStreamId, {
            $inc: { totalReactions: 1 }
        });
    }

    async pinMessage(liveStreamId: string, message: string, pinnedBy: string): Promise<ILiveStream> {
        return this.updateById(liveStreamId, {
            pinnedMessage: {
                message,
                pinnedAt: new Date(),
                pinnedBy
            }
        });
    }

    async banUser(liveStreamId: string, userId: string, hostId: string): Promise<ILiveStream> {
        const liveStream = await this.findByIdOrThrow(liveStreamId);
        
        if (liveStream.hostId.toString() !== hostId) {
            throw new ForbiddenException("Only the host can ban users");
        }

        if (liveStream.bannedUsers.includes(userId)) {
            throw new BadRequestException("User is already banned");
        }

        return this.model.findByIdAndUpdate(
            liveStreamId,
            { $addToSet: { bannedUsers: userId } },
            { new: true }
        ).exec();
    }

    async unbanUser(liveStreamId: string, userId: string, hostId: string): Promise<ILiveStream> {
        const liveStream = await this.findByIdOrThrow(liveStreamId);
        
        if (liveStream.hostId.toString() !== hostId) {
            throw new ForbiddenException("Only the host can unban users");
        }

        return this.model.findByIdAndUpdate(
            liveStreamId,
            { $pull: { bannedUsers: userId } },
            { new: true }
        ).exec();
    }

    async checkUserAccess(liveStreamId: string, userId: string): Promise<boolean> {
        const liveStream = await this.findByIdOrThrow(liveStreamId);
        
        // Check if user is banned
        if (liveStream.bannedUsers.includes(userId)) {
            return false;
        }

        // Check privacy settings
        if (liveStream.privacy === LiveStreamPrivacy.Public) {
            return true;
        }

        if (liveStream.privacy === LiveStreamPrivacy.SpecificUsers) {
            return liveStream.allowedUsers.includes(userId) || liveStream.hostId.toString() === userId;
        }

        return false;
    }
}
