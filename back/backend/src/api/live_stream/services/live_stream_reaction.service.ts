/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, QueryOptions, PaginateModel, UpdateQuery } from 'mongoose';
import { ILiveStreamReaction } from '../entities/live_stream_reaction.entity';
import { BaseService } from '../../../core/common/base.service';
import { LiveStreamReactionType } from '../../../core/utils/enums';
import { isValidMongoId } from '../../../core/utils/utils';

@Injectable()
export class LiveStreamReactionService extends BaseService<ILiveStreamReaction> {
    constructor(
        @InjectModel('live_stream_reaction') private readonly model: PaginateModel<ILiveStreamReaction>,
    ) {
        super();
    }

    // Implement missing abstract methods from BaseService
    async createMany(obj: Partial<ILiveStreamReaction>[], session?: any): Promise<any> {
        return this.model.create(obj, { session });
    }

    async findOneAndUpdate(filter: FilterQuery<ILiveStreamReaction>, update: UpdateQuery<ILiveStreamReaction>, session?: any, options?: QueryOptions<ILiveStreamReaction>): Promise<ILiveStreamReaction | null> {
        return this.model.findOneAndUpdate(filter, update, { ...options, session, new: true }).lean();
    }

    async findByIdAndUpdate(id: string, update: UpdateQuery<ILiveStreamReaction>, session?: any): Promise<ILiveStreamReaction | null> {
        if (!isValidMongoId(id)) {
            throw new BadRequestException("Invalid MongoDB ObjectId: " + id);
        }
        return this.model.findByIdAndUpdate(id, update, { session, new: true }).lean();
    }

    async updateMany(filter: FilterQuery<ILiveStreamReaction>, update: UpdateQuery<ILiveStreamReaction>, session?: any, options?: QueryOptions<ILiveStreamReaction>): Promise<any> {
        return this.model.updateMany(filter, update, { ...options, session });
    }

    async findByIdAndDelete(id: string, session?: any): Promise<ILiveStreamReaction | null> {
        if (!isValidMongoId(id)) {
            throw new BadRequestException("Invalid MongoDB ObjectId: " + id);
        }
        return this.model.findByIdAndDelete(id, { session }).lean();
    }

    async deleteMany(filter: FilterQuery<ILiveStreamReaction>, session?: any): Promise<any> {
        return this.model.deleteMany(filter, { session });
    }

    async findCount(filter: FilterQuery<ILiveStreamReaction>, session?: any): Promise<number> {
        return this.model.countDocuments(filter, { session });
    }

    async create(data: Partial<ILiveStreamReaction>): Promise<ILiveStreamReaction> {
        const created = await this.model.create({
            ...data,
            timestamp: new Date()
        });
        return this.findById(created._id.toString());
    }

    async findById(id: string, select?: string): Promise<ILiveStreamReaction | null> {
        if (!isValidMongoId(id)) {
            throw new BadRequestException("Invalid MongoDB ObjectId: " + id);
        }
        return this.model.findById(id, select).populate('userId', 'fullName userImage').lean();
    }

    async findByIdOrThrow(id: string, select?: string): Promise<ILiveStreamReaction> {
        const reaction = await this.findById(id, select);
        if (!reaction) {
            throw new NotFoundException("Reaction with id " + id + " not found");
        }
        return reaction;
    }

    async findOne(filter: FilterQuery<ILiveStreamReaction>, select?: string): Promise<ILiveStreamReaction | null> {
        return this.model.findOne(filter, select).populate('userId', 'fullName userImage').lean();
    }

    async findAll(
        filter?: FilterQuery<ILiveStreamReaction>,
        select?: string,
        options?: QueryOptions<ILiveStreamReaction>
    ): Promise<ILiveStreamReaction[]> {
        return this.model.find(filter, select, options).populate('userId', 'fullName userImage').lean();
    }

    async updateById(id: string, update: Partial<ILiveStreamReaction>): Promise<ILiveStreamReaction | null> {
        return this.model.findByIdAndUpdate(id, update, { new: true }).populate('userId', 'fullName userImage').lean();
    }

    async deleteById(id: string): Promise<any> {
        return this.model.findByIdAndDelete(id);
    }

    async addReaction(liveStreamId: string, userId: string, reactionType: LiveStreamReactionType): Promise<ILiveStreamReaction> {
        return this.create({
            liveStreamId,
            userId,
            reactionType,
            timestamp: new Date()
        });
    }

    async getReactions(liveStreamId: string, page: number = 1, limit: number = 50) {
        const filter = {
            liveStreamId
        };

        const options = {
            page,
            limit,
            sort: { timestamp: -1 },
            populate: [
                { path: 'userId', select: 'fullName userImage' }
            ]
        };

        return this.model.paginate(filter, options);
    }

    async getRecentReactions(liveStreamId: string, limit: number = 20): Promise<ILiveStreamReaction[]> {
        return this.findAll(
            { liveStreamId },
            null,
            {
                sort: { timestamp: -1 },
                limit
            }
        );
    }

    async getReactionStats(liveStreamId: string): Promise<any> {
        const stats = await this.model.aggregate([
            { $match: { liveStreamId: isValidMongoId(liveStreamId) ? liveStreamId : null } },
            {
                $group: {
                    _id: '$reactionType',
                    count: { $sum: 1 }
                }
            },
            {
                $group: {
                    _id: null,
                    reactions: {
                        $push: {
                            type: '$_id',
                            count: '$count'
                        }
                    },
                    totalReactions: { $sum: '$count' }
                }
            }
        ]);

        if (stats.length === 0) {
            return {
                reactions: [],
                totalReactions: 0
            };
        }

        return {
            reactions: stats[0].reactions,
            totalReactions: stats[0].totalReactions
        };
    }

    async getUserReactions(liveStreamId: string, userId: string, page: number = 1, limit: number = 20) {
        const filter = {
            liveStreamId,
            userId
        };

        const options = {
            page,
            limit,
            sort: { timestamp: -1 },
            populate: [
                { path: 'userId', select: 'fullName userImage' }
            ]
        };

        return this.model.paginate(filter, options);
    }

    async getReactionCount(liveStreamId: string): Promise<number> {
        return this.model.countDocuments({ liveStreamId });
    }

    async getReactionCountByType(liveStreamId: string, reactionType: LiveStreamReactionType): Promise<number> {
        return this.model.countDocuments({ 
            liveStreamId, 
            reactionType 
        });
    }

    async deleteUserReactions(liveStreamId: string, userId: string): Promise<void> {
        await this.model.deleteMany({
            liveStreamId,
            userId
        });
    }

    async getTopReactions(liveStreamId: string, limit: number = 5): Promise<any[]> {
        return this.model.aggregate([
            { $match: { liveStreamId: isValidMongoId(liveStreamId) ? liveStreamId : null } },
            {
                $group: {
                    _id: '$reactionType',
                    count: { $sum: 1 },
                    lastReaction: { $max: '$timestamp' }
                }
            },
            { $sort: { count: -1, lastReaction: -1 } },
            { $limit: limit },
            {
                $project: {
                    reactionType: '$_id',
                    count: 1,
                    lastReaction: 1,
                    _id: 0
                }
            }
        ]);
    }
}
