/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, QueryOptions, PaginateModel, UpdateQuery } from 'mongoose';
import { ILiveStreamComment } from '../entities/live_stream_comment.entity';
import { BaseService } from '../../../core/common/base.service';
import { isValidMongoId } from '../../../core/utils/utils';

@Injectable()
export class LiveStreamCommentService extends BaseService<ILiveStreamComment> {
    constructor(
        @InjectModel('live_stream_comment') private readonly model: PaginateModel<ILiveStreamComment>,
    ) {
        super();
    }

    // Implement missing abstract methods from BaseService
    async createMany(obj: Partial<ILiveStreamComment>[], session?: any): Promise<any> {
        return this.model.create(obj, { session });
    }

    async findOneAndUpdate(filter: FilterQuery<ILiveStreamComment>, update: UpdateQuery<ILiveStreamComment>, session?: any, options?: QueryOptions<ILiveStreamComment>): Promise<ILiveStreamComment | null> {
        return this.model.findOneAndUpdate(filter, update, { ...options, session, new: true }).lean();
    }

    async findByIdAndUpdate(id: string, update: UpdateQuery<ILiveStreamComment>, session?: any): Promise<ILiveStreamComment | null> {
        if (!isValidMongoId(id)) {
            throw new BadRequestException("Invalid MongoDB ObjectId: " + id);
        }
        return this.model.findByIdAndUpdate(id, update, { session, new: true }).lean();
    }

    async updateMany(filter: FilterQuery<ILiveStreamComment>, update: UpdateQuery<ILiveStreamComment>, session?: any, options?: QueryOptions<ILiveStreamComment>): Promise<any> {
        return this.model.updateMany(filter, update, { ...options, session });
    }

    async findByIdAndDelete(id: string, session?: any): Promise<ILiveStreamComment | null> {
        if (!isValidMongoId(id)) {
            throw new BadRequestException("Invalid MongoDB ObjectId: " + id);
        }
        return this.model.findByIdAndDelete(id, { session }).lean();
    }

    async deleteMany(filter: FilterQuery<ILiveStreamComment>, session?: any): Promise<any> {
        return this.model.deleteMany(filter, { session });
    }

    async findCount(filter: FilterQuery<ILiveStreamComment>, session?: any): Promise<number> {
        return this.model.countDocuments(filter, { session });
    }

    async create(data: Partial<ILiveStreamComment>): Promise<ILiveStreamComment> {
        const created = await this.model.create({
            ...data,
            timestamp: new Date()
        });
        return this.findById(created._id.toString());
    }

    async findById(id: string, select?: string): Promise<ILiveStreamComment | null> {
        if (!isValidMongoId(id)) {
            throw new BadRequestException("Invalid MongoDB ObjectId: " + id);
        }
        return this.model.findById(id, select).populate('userId', 'fullName userImage').lean();
    }

    async findByIdOrThrow(id: string, select?: string): Promise<ILiveStreamComment> {
        const comment = await this.findById(id, select);
        if (!comment) {
            throw new NotFoundException("Comment with id " + id + " not found");
        }
        return comment;
    }

    async findOne(filter: FilterQuery<ILiveStreamComment>, select?: string): Promise<ILiveStreamComment | null> {
        return this.model.findOne(filter, select).populate('userId', 'fullName userImage').lean();
    }

    async findAll(
        filter?: FilterQuery<ILiveStreamComment>,
        select?: string,
        options?: QueryOptions<ILiveStreamComment>
    ): Promise<ILiveStreamComment[]> {
        return this.model.find(filter, select, options).populate('userId', 'fullName userImage').lean();
    }

    async updateById(id: string, update: Partial<ILiveStreamComment>): Promise<ILiveStreamComment | null> {
        return this.model.findByIdAndUpdate(id, update, { new: true }).populate('userId', 'fullName userImage').lean();
    }

    async deleteById(id: string): Promise<any> {
        return this.model.findByIdAndDelete(id);
    }

    async addComment(liveStreamId: string, userId: string, message: string): Promise<ILiveStreamComment> {
        return this.create({
            liveStreamId,
            userId,
            message,
            timestamp: new Date()
        });
    }

    async getComments(liveStreamId: string, page: number = 1, limit: number = 50) {
        const filter = {
            liveStreamId,
            isDeleted: false
        };

        const options = {
            page,
            limit,
            sort: { timestamp: -1 },
            populate: [
                { path: 'userId', select: 'fullName userImage' }
            ]
        };

        return this.model.paginate(filter, options);
    }

    async getRecentComments(liveStreamId: string, limit: number = 20): Promise<ILiveStreamComment[]> {
        return this.findAll(
            {
                liveStreamId,
                isDeleted: false
            },
            null,
            {
                sort: { timestamp: -1 },
                limit
            }
        );
    }

    async deleteComment(commentId: string, deletedBy: string): Promise<ILiveStreamComment | null> {
        return this.updateById(commentId, {
            isDeleted: true,
            deletedAt: new Date(),
            deletedBy
        });
    }

    async pinComment(commentId: string, pinnedBy: string): Promise<ILiveStreamComment | null> {
        // First, unpin any existing pinned comment for this live stream
        const comment = await this.findByIdOrThrow(commentId);
        
        await this.model.updateMany(
            {
                liveStreamId: comment.liveStreamId,
                isPinned: true
            },
            {
                isPinned: false,
                pinnedAt: null,
                pinnedBy: null
            }
        );

        // Pin the new comment
        return this.updateById(commentId, {
            isPinned: true,
            pinnedAt: new Date(),
            pinnedBy
        });
    }

    async unpinComment(commentId: string): Promise<ILiveStreamComment | null> {
        return this.updateById(commentId, {
            isPinned: false,
            pinnedAt: null,
            pinnedBy: null
        });
    }

    async getPinnedComment(liveStreamId: string): Promise<ILiveStreamComment | null> {
        return this.findOne({
            liveStreamId,
            isPinned: true,
            isDeleted: false
        });
    }

    async getCommentCount(liveStreamId: string): Promise<number> {
        return this.model.countDocuments({
            liveStreamId,
            isDeleted: false
        });
    }

    async getUserComments(liveStreamId: string, userId: string, page: number = 1, limit: number = 20) {
        const filter = {
            liveStreamId,
            userId,
            isDeleted: false
        };

        const options = {
            page,
            limit,
            sort: { timestamp: -1 },
            populate: [
                { path: 'userId', select: 'fullName userImage' }
            ]
        };

        return this.model.paginate(filter, options);
    }

    async deleteUserComments(liveStreamId: string, userId: string, deletedBy: string): Promise<void> {
        await this.model.updateMany(
            {
                liveStreamId,
                userId,
                isDeleted: false
            },
            {
                isDeleted: true,
                deletedAt: new Date(),
                deletedBy
            }
        );
    }
}
