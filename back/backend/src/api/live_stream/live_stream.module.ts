/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { LiveStreamController } from './live_stream.controller';
import { LiveStreamService } from './services/live_stream.service';
import { LiveStreamParticipantService } from './services/live_stream_participant.service';
import { LiveStreamCommentService } from './services/live_stream_comment.service';
import { LiveStreamReactionService } from './services/live_stream_reaction.service';
import { LiveStreamSchema } from './entities/live_stream.entity';
import { LiveStreamParticipantSchema } from './entities/live_stream_participant.entity';
import { LiveStreamCommentSchema } from './entities/live_stream_comment.entity';
import { LiveStreamReactionSchema } from './entities/live_stream_reaction.entity';
import { AgoraModule } from '../../chat/agora/agora.module';
import { SocketIoModule } from '../../chat/socket_io/socket_io.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  controllers: [LiveStreamController],
  providers: [
    LiveStreamService,
    LiveStreamParticipantService,
    LiveStreamCommentService,
    LiveStreamReactionService,
  ],
  imports: [
    MongooseModule.forFeature([
      {
        name: 'live_stream',
        schema: LiveStreamSchema,
      },
      {
        name: 'live_stream_participant',
        schema: LiveStreamParticipantSchema,
      },
      {
        name: 'live_stream_comment',
        schema: LiveStreamCommentSchema,
      },
      {
        name: 'live_stream_reaction',
        schema: LiveStreamReactionSchema,
      },
    ]),
    AgoraModule,
    SocketIoModule,
    AuthModule,
  ],
  exports: [
    LiveStreamService,
    LiveStreamParticipantService,
    LiveStreamCommentService,
    LiveStreamReactionService,
  ],
})
export class LiveStreamModule {}
