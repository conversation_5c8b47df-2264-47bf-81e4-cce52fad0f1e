/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import { IsString, IsOptional, IsEnum, IsArray, MaxLength, MinLength } from 'class-validator';
import { LiveStreamPrivacy } from '../../../core/utils/enums';
import { IUser } from '../../user_modules/user/entities/user.entity';

export class CreateLiveStreamDto {
    @IsString()
    @MinLength(1)
    @MaxLength(100)
    title: string;

    @IsOptional()
    @IsString()
    @MaxLength(500)
    description?: string;

    @IsEnum(LiveStreamPrivacy)
    privacy: LiveStreamPrivacy;

    @IsOptional()
    @IsArray()
    allowedUsers?: string[];

    @IsOptional()
    filters?: {
        filterType: string;
        filterData: any;
    };

    // Set by middleware
    myUser?: IUser;
}
