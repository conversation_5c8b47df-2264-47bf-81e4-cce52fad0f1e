/**
 * Copyright 2023, the <PERSON><PERSON><PERSON>b project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import { IsString, IsOptional, IsEnum, IsArray, MaxLength, IsMongoId } from 'class-validator';
import { LiveStreamReactionType } from '../../../core/utils/enums';
import { IUser } from '../../user_modules/user/entities/user.entity';

export class JoinLiveStreamDto {
    @IsMongoId()
    liveStreamId: string;

    // Set by middleware
    myUser?: IUser;
}

export class LeaveLiveStreamDto {
    @IsMongoId()
    liveStreamId: string;

    // Set by middleware
    myUser?: IUser;
}

export class InviteUsersDto {
    @IsMongoId()
    liveStreamId: string;

    @IsArray()
    @IsMongoId({ each: true })
    userIds: string[];

    // Set by middleware
    myUser?: IUser;
}

export class BanUserDto {
    @IsMongoId()
    liveStreamId: string;

    @IsMongoId()
    userId: string;

    // Set by middleware
    myUser?: IUser;
}

export class UnbanUserDto {
    @IsMongoId()
    liveStreamId: string;

    @IsMongoId()
    userId: string;

    // Set by middleware
    myUser?: IUser;
}

export class PinMessageDto {
    @IsMongoId()
    liveStreamId: string;

    @IsString()
    @MaxLength(500)
    message: string;

    // Set by middleware
    myUser?: IUser;
}

export class AddCommentDto {
    @IsMongoId()
    liveStreamId: string;

    @IsString()
    @MaxLength(500)
    message: string;

    // Set by middleware
    myUser?: IUser;
}

export class AddReactionDto {
    @IsMongoId()
    liveStreamId: string;

    @IsEnum(LiveStreamReactionType)
    reactionType: LiveStreamReactionType;

    // Set by middleware
    myUser?: IUser;
}

export class MuteLiveStreamDto {
    @IsMongoId()
    liveStreamId: string;

    // Set by middleware
    myUser?: IUser;
}
