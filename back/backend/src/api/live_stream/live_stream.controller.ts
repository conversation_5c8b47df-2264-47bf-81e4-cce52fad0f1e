/**
 * Copyright 2023, the hate<PERSON><PERSON>b project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import {
    Controller,
    Get,
    Post,
    Body,
    Patch,
    Param,
    Delete,
    UseGuards,
    Req,
    Query,
    BadRequestException,
    ForbiddenException
} from '@nestjs/common';
import { LiveStreamService } from './services/live_stream.service';
import { LiveStreamParticipantService } from './services/live_stream_participant.service';
import { LiveStreamCommentService } from './services/live_stream_comment.service';
import { LiveStreamReactionService } from './services/live_stream_reaction.service';
import { VerifiedAuthGuard } from '../../core/guards/verified.auth.guard';
import { V1Controller } from '../../core/common/v1-controller.decorator';
import { resOK } from '../../core/utils/res.helpers';
import { CreateLiveStreamDto } from './dto/create-live-stream.dto';
import {
    JoinLiveStreamDto,
    LeaveLiveStreamDto,
    InviteUsersDto,
    BanUserDto,
    UnbanUserDto,
    PinMessageDto,
    AddCommentDto,
    AddReactionDto,
    MuteLiveStreamDto
} from './dto/live-stream-action.dto';
import { MongoIdDto } from '../../core/common/dto/mongo.id.dto';
import { AgoraService } from '../../chat/agora/agora.service';
import { SocketIoService } from '../../chat/socket_io/socket_io.service';
import { SocketEventsType, LiveStreamParticipantRole } from '../../core/utils/enums';

@UseGuards(VerifiedAuthGuard)
@V1Controller('live-stream')
export class LiveStreamController {
    constructor(
        private readonly liveStreamService: LiveStreamService,
        private readonly participantService: LiveStreamParticipantService,
        private readonly commentService: LiveStreamCommentService,
        private readonly reactionService: LiveStreamReactionService,
        private readonly agoraService: AgoraService,
        private readonly socketService: SocketIoService,
    ) {}

    @Post('/start')
    async startLiveStream(
        @Body() dto: CreateLiveStreamDto,
        @Req() req: any,
    ) {
        dto.myUser = req.user;
        
        // Create live stream
        const liveStream = await this.liveStreamService.create(dto);
        
        // Add host as participant
        await this.participantService.joinLiveStream(
            liveStream._id,
            dto.myUser._id.toString(),
            LiveStreamParticipantRole.Host
        );
        
        // Get Agora token for host
        const agoraToken = this.agoraService.getLiveStreamHostAccess(
            liveStream.agoraChannelName,
            dto.myUser._id.toString()
        );
        
        // Emit live stream started event
        this.socketService.io.emit(SocketEventsType.v1OnLiveStreamStarted, JSON.stringify({
            liveStream,
            agoraToken
        }));
        
        return resOK({
            liveStream,
            agoraToken
        });
    }

    @Post('/:id/end')
    async endLiveStream(
        @Param() param: MongoIdDto,
        @Req() req: any,
    ) {
        const liveStream = await this.liveStreamService.endLiveStream(param.id, req.user._id.toString());
        
        // Emit live stream ended event
        this.socketService.io.to(liveStream.agoraChannelName).emit(
            SocketEventsType.v1OnLiveStreamEnded,
            JSON.stringify({ liveStreamId: liveStream._id })
        );
        
        return resOK(liveStream);
    }

    @Get('/active')
    async getActiveLiveStreams(@Req() req: any, @Query() query: any) {
        const page = parseInt(query.page) || 1;
        const limit = parseInt(query.limit) || 20;
        
        const liveStreams = await this.liveStreamService.getActiveLiveStreams(
            req.user._id.toString(),
            page,
            limit
        );
        
        return resOK(liveStreams);
    }

    @Post('/:id/join')
    async joinLiveStream(
        @Param() param: MongoIdDto,
        @Req() req: any,
    ) {
        const userId = req.user._id.toString();
        
        // Check user access
        const hasAccess = await this.liveStreamService.checkUserAccess(param.id, userId);
        if (!hasAccess) {
            throw new ForbiddenException("You don't have access to this live stream");
        }
        
        // Join as participant
        const participant = await this.participantService.joinLiveStream(param.id, userId);
        
        // Update viewer count
        await this.liveStreamService.updateViewerCount(param.id, 1);
        
        // Get Agora token for viewer
        const liveStream = await this.liveStreamService.findByIdOrThrow(param.id);
        const agoraToken = this.agoraService.getLiveStreamViewerAccess(
            liveStream.agoraChannelName,
            userId
        );
        
        // Emit user joined event
        this.socketService.io.to(liveStream.agoraChannelName).emit(
            SocketEventsType.v1OnLiveStreamUserJoined,
            JSON.stringify({
                liveStreamId: param.id,
                participant,
                viewerCount: await this.participantService.getParticipantCount(param.id)
            })
        );
        
        return resOK({
            participant,
            agoraToken,
            liveStream
        });
    }

    @Post('/:id/leave')
    async leaveLiveStream(
        @Param() param: MongoIdDto,
        @Req() req: any,
    ) {
        const userId = req.user._id.toString();
        
        // Leave as participant
        const participant = await this.participantService.leaveLiveStream(param.id, userId);
        
        if (participant) {
            // Update viewer count
            await this.liveStreamService.updateViewerCount(param.id, -1);
            
            // Get live stream for channel name
            const liveStream = await this.liveStreamService.findByIdOrThrow(param.id);
            
            // Emit user left event
            this.socketService.io.to(liveStream.agoraChannelName).emit(
                SocketEventsType.v1OnLiveStreamUserLeft,
                JSON.stringify({
                    liveStreamId: param.id,
                    userId,
                    viewerCount: await this.participantService.getParticipantCount(param.id)
                })
            );
        }
        
        return resOK({ success: true });
    }

    @Post('/:id/invite')
    async inviteUsers(
        @Body() dto: InviteUsersDto,
        @Param() param: MongoIdDto,
        @Req() req: any,
    ) {
        dto.liveStreamId = param.id;
        dto.myUser = req.user;
        
        const liveStream = await this.liveStreamService.findByIdOrThrow(param.id);
        
        // Check if user is host
        if (liveStream.hostId.toString() !== req.user._id.toString()) {
            throw new ForbiddenException("Only the host can invite users");
        }
        
        // Send invitations (implement notification service)
        // For now, just return success
        
        return resOK({ success: true, invitedUsers: dto.userIds });
    }

    @Post('/:id/ban')
    async banUser(
        @Body() dto: BanUserDto,
        @Param() param: MongoIdDto,
        @Req() req: any,
    ) {
        dto.liveStreamId = param.id;
        dto.myUser = req.user;
        
        // Ban user from live stream
        const liveStream = await this.liveStreamService.banUser(
            param.id,
            dto.userId,
            req.user._id.toString()
        );
        
        // Ban participant
        await this.participantService.banParticipant(param.id, dto.userId);
        
        // Emit user banned event
        this.socketService.io.to(liveStream.agoraChannelName).emit(
            SocketEventsType.v1OnLiveStreamUserBanned,
            JSON.stringify({
                liveStreamId: param.id,
                userId: dto.userId
            })
        );
        
        return resOK({ success: true });
    }

    @Post('/:id/unban')
    async unbanUser(
        @Body() dto: UnbanUserDto,
        @Param() param: MongoIdDto,
        @Req() req: any,
    ) {
        dto.liveStreamId = param.id;
        dto.myUser = req.user;
        
        // Unban user from live stream
        const liveStream = await this.liveStreamService.unbanUser(
            param.id,
            dto.userId,
            req.user._id.toString()
        );
        
        // Unban participant
        await this.participantService.unbanParticipant(param.id, dto.userId);
        
        return resOK({ success: true });
    }

    @Post('/:id/pin-message')
    async pinMessage(
        @Body() dto: PinMessageDto,
        @Param() param: MongoIdDto,
        @Req() req: any,
    ) {
        dto.liveStreamId = param.id;
        dto.myUser = req.user;
        
        const liveStream = await this.liveStreamService.findByIdOrThrow(param.id);
        
        // Check if user is host
        if (liveStream.hostId.toString() !== req.user._id.toString()) {
            throw new ForbiddenException("Only the host can pin messages");
        }
        
        // Pin message
        const updatedLiveStream = await this.liveStreamService.pinMessage(
            param.id,
            dto.message,
            req.user._id.toString()
        );
        
        // Emit message pinned event
        this.socketService.io.to(liveStream.agoraChannelName).emit(
            SocketEventsType.v1OnLiveStreamMessagePinned,
            JSON.stringify({
                liveStreamId: param.id,
                pinnedMessage: updatedLiveStream.pinnedMessage
            })
        );
        
        return resOK(updatedLiveStream.pinnedMessage);
    }

    @Post('/:id/comment')
    async addComment(
        @Body() dto: AddCommentDto,
        @Param() param: MongoIdDto,
        @Req() req: any,
    ) {
        dto.liveStreamId = param.id;
        dto.myUser = req.user;

        const userId = req.user._id.toString();

        // Check if user is participant
        const isParticipant = await this.participantService.isUserParticipant(param.id, userId);
        if (!isParticipant) {
            throw new ForbiddenException("You must join the live stream to comment");
        }

        // Add comment
        const comment = await this.commentService.addComment(param.id, userId, dto.message);

        // Increment comment count
        await this.liveStreamService.incrementCommentCount(param.id);

        // Get live stream for channel name
        const liveStream = await this.liveStreamService.findByIdOrThrow(param.id);

        // Emit new comment event
        this.socketService.io.to(liveStream.agoraChannelName).emit(
            SocketEventsType.v1OnLiveStreamNewComment,
            JSON.stringify({
                liveStreamId: param.id,
                comment
            })
        );

        return resOK(comment);
    }

    @Post('/:id/react')
    async addReaction(
        @Body() dto: AddReactionDto,
        @Param() param: MongoIdDto,
        @Req() req: any,
    ) {
        dto.liveStreamId = param.id;
        dto.myUser = req.user;

        const userId = req.user._id.toString();

        // Check if user is participant
        const isParticipant = await this.participantService.isUserParticipant(param.id, userId);
        if (!isParticipant) {
            throw new ForbiddenException("You must join the live stream to react");
        }

        // Add reaction
        const reaction = await this.reactionService.addReaction(param.id, userId, dto.reactionType);

        // Increment reaction count
        await this.liveStreamService.incrementReactionCount(param.id);

        // Get live stream for channel name
        const liveStream = await this.liveStreamService.findByIdOrThrow(param.id);

        // Emit new reaction event
        this.socketService.io.to(liveStream.agoraChannelName).emit(
            SocketEventsType.v1OnLiveStreamNewReaction,
            JSON.stringify({
                liveStreamId: param.id,
                reaction
            })
        );

        return resOK(reaction);
    }

    @Get('/:id')
    async getLiveStream(
        @Param() param: MongoIdDto,
        @Req() req: any,
    ) {
        const liveStream = await this.liveStreamService.findByIdOrThrow(param.id);

        // Check user access
        const hasAccess = await this.liveStreamService.checkUserAccess(param.id, req.user._id.toString());
        if (!hasAccess) {
            throw new ForbiddenException("You don't have access to this live stream");
        }

        return resOK(liveStream);
    }

    @Get('/:id/participants')
    async getParticipants(
        @Param() param: MongoIdDto,
        @Query() query: any,
        @Req() req: any,
    ) {
        const page = parseInt(query.page) || 1;
        const limit = parseInt(query.limit) || 50;

        // Check user access
        const hasAccess = await this.liveStreamService.checkUserAccess(param.id, req.user._id.toString());
        if (!hasAccess) {
            throw new ForbiddenException("You don't have access to this live stream");
        }

        const participants = await this.participantService.getActiveParticipants(param.id, page, limit);

        return resOK(participants);
    }

    @Get('/:id/comments')
    async getComments(
        @Param() param: MongoIdDto,
        @Query() query: any,
        @Req() req: any,
    ) {
        const page = parseInt(query.page) || 1;
        const limit = parseInt(query.limit) || 50;

        // Check user access
        const hasAccess = await this.liveStreamService.checkUserAccess(param.id, req.user._id.toString());
        if (!hasAccess) {
            throw new ForbiddenException("You don't have access to this live stream");
        }

        const comments = await this.commentService.getComments(param.id, page, limit);

        return resOK(comments);
    }

    @Get('/:id/reactions')
    async getReactions(
        @Param() param: MongoIdDto,
        @Query() query: any,
        @Req() req: any,
    ) {
        const page = parseInt(query.page) || 1;
        const limit = parseInt(query.limit) || 50;

        // Check user access
        const hasAccess = await this.liveStreamService.checkUserAccess(param.id, req.user._id.toString());
        if (!hasAccess) {
            throw new ForbiddenException("You don't have access to this live stream");
        }

        const reactions = await this.reactionService.getReactions(param.id, page, limit);
        const stats = await this.reactionService.getReactionStats(param.id);

        return resOK({
            reactions,
            stats
        });
    }

    @Get('/:id/agora-token')
    async getAgoraToken(
        @Param() param: MongoIdDto,
        @Req() req: any,
    ) {
        const liveStream = await this.liveStreamService.findByIdOrThrow(param.id);

        // Check user access
        const hasAccess = await this.liveStreamService.checkUserAccess(param.id, req.user._id.toString());
        if (!hasAccess) {
            throw new ForbiddenException("You don't have access to this live stream");
        }

        // Determine role and get appropriate token
        const isHost = liveStream.hostId.toString() === req.user._id.toString();
        const agoraToken = isHost
            ? this.agoraService.getLiveStreamHostAccess(liveStream.agoraChannelName, req.user._id.toString())
            : this.agoraService.getLiveStreamViewerAccess(liveStream.agoraChannelName, req.user._id.toString());

        return resOK(agoraToken);
    }
}
