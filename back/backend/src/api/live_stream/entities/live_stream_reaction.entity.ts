/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import { Schema, Document } from "mongoose";
import { LiveStreamReactionType } from "../../../core/utils/enums";
import aggregatePaginate from "mongoose-aggregate-paginate-v2";

export interface ILiveStreamReaction extends Document {
    _id: string;
    liveStreamId: string;
    userId: string;
    reactionType: LiveStreamReactionType;
    timestamp: Date;
    createdAt: Date;
    updatedAt: Date;
}

export const LiveStreamReactionSchema: Schema = new Schema(
    {
        liveStreamId: { 
            type: Schema.Types.ObjectId, 
            required: true, 
            ref: 'live_stream',
            index: 1
        },
        userId: { 
            type: Schema.Types.ObjectId, 
            required: true, 
            ref: 'user',
            index: 1
        },
        reactionType: { 
            type: String, 
            enum: Object.values(LiveStreamReactionType),
            required: true
        },
        timestamp: { 
            type: Date, 
            default: Date.now,
            index: 1
        }
    },
    {
        timestamps: true,
    }
);

// Indexes for better performance
LiveStreamReactionSchema.index({ liveStreamId: 1, timestamp: -1 });
LiveStreamReactionSchema.index({ liveStreamId: 1, reactionType: 1 });
LiveStreamReactionSchema.index({ userId: 1, timestamp: -1 });
LiveStreamReactionSchema.index({ liveStreamId: 1, userId: 1, timestamp: -1 });

// Add pagination plugin
LiveStreamReactionSchema.plugin(aggregatePaginate);

// Auto-expire reactions after 24 hours
LiveStreamReactionSchema.index({ createdAt: 1 }, { expireAfterSeconds: 86400 });
