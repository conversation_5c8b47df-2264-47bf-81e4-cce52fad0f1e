/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import { Schema, Document } from "mongoose";
import { LiveStreamParticipantRole } from "../../../core/utils/enums";
import aggregatePaginate from "mongoose-aggregate-paginate-v2";

export interface ILiveStreamParticipant extends Document {
    _id: string;
    liveStreamId: string;
    userId: string;
    role: LiveStreamParticipantRole;
    joinedAt: Date;
    leftAt?: Date;
    isMuted: boolean;
    isBanned: boolean;
    watchDuration: number; // in seconds
    lastActiveAt: Date;
    createdAt: Date;
    updatedAt: Date;
}

export const LiveStreamParticipantSchema: Schema = new Schema(
    {
        liveStreamId: { 
            type: Schema.Types.ObjectId, 
            required: true, 
            ref: 'live_stream',
            index: 1
        },
        userId: { 
            type: Schema.Types.ObjectId, 
            required: true, 
            ref: 'user',
            index: 1
        },
        role: { 
            type: String, 
            enum: Object.values(LiveStreamParticipantRole),
            default: LiveStreamParticipantRole.Viewer
        },
        joinedAt: { 
            type: Date, 
            default: Date.now 
        },
        leftAt: { 
            type: Date 
        },
        isMuted: { 
            type: Boolean, 
            default: false 
        },
        isBanned: { 
            type: Boolean, 
            default: false 
        },
        watchDuration: { 
            type: Number, 
            default: 0 
        },
        lastActiveAt: { 
            type: Date, 
            default: Date.now 
        }
    },
    {
        timestamps: true,
    }
);

// Compound indexes for better performance
LiveStreamParticipantSchema.index({ liveStreamId: 1, userId: 1 }, { unique: true });
LiveStreamParticipantSchema.index({ liveStreamId: 1, role: 1 });
LiveStreamParticipantSchema.index({ liveStreamId: 1, isBanned: 1 });
LiveStreamParticipantSchema.index({ userId: 1, joinedAt: -1 });

// Add pagination plugin
LiveStreamParticipantSchema.plugin(aggregatePaginate);
