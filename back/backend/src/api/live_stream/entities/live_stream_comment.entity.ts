/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import { Schema, Document } from "mongoose";
import aggregatePaginate from "mongoose-aggregate-paginate-v2";

export interface ILiveStreamComment extends Document {
    _id: string;
    liveStreamId: string;
    userId: string;
    message: string;
    timestamp: Date;
    isDeleted: boolean;
    deletedAt?: Date;
    deletedBy?: string;
    isPinned: boolean;
    pinnedAt?: Date;
    pinnedBy?: string;
    createdAt: Date;
    updatedAt: Date;
}

export const LiveStreamCommentSchema: Schema = new Schema(
    {
        liveStreamId: { 
            type: Schema.Types.ObjectId, 
            required: true, 
            ref: 'live_stream',
            index: 1
        },
        userId: { 
            type: Schema.Types.ObjectId, 
            required: true, 
            ref: 'user',
            index: 1
        },
        message: { 
            type: String, 
            required: true,
            maxlength: 500
        },
        timestamp: { 
            type: Date, 
            default: Date.now,
            index: 1
        },
        isDeleted: { 
            type: Boolean, 
            default: false 
        },
        deletedAt: { 
            type: Date 
        },
        deletedBy: { 
            type: Schema.Types.ObjectId, 
            ref: 'user' 
        },
        isPinned: { 
            type: Boolean, 
            default: false 
        },
        pinnedAt: { 
            type: Date 
        },
        pinnedBy: { 
            type: Schema.Types.ObjectId, 
            ref: 'user' 
        }
    },
    {
        timestamps: true,
    }
);

// Indexes for better performance
LiveStreamCommentSchema.index({ liveStreamId: 1, timestamp: -1 });
LiveStreamCommentSchema.index({ liveStreamId: 1, isDeleted: 1, timestamp: -1 });
LiveStreamCommentSchema.index({ liveStreamId: 1, isPinned: 1 });
LiveStreamCommentSchema.index({ userId: 1, timestamp: -1 });

// Add pagination plugin
LiveStreamCommentSchema.plugin(aggregatePaginate);

// Auto-expire comments after 7 days
LiveStreamCommentSchema.index({ createdAt: 1 }, { expireAfterSeconds: 604800 });
