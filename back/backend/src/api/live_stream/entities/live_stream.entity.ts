/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import { Schema, Document } from "mongoose";
import { LiveStreamStatus, LiveStreamPrivacy } from "../../../core/utils/enums";
import aggregatePaginate from "mongoose-aggregate-paginate-v2";

export interface ILiveStream extends Document {
    _id: string;
    hostId: string;
    title: string;
    description?: string;
    privacy: LiveStreamPrivacy;
    allowedUsers: string[];
    bannedUsers: string[];
    status: LiveStreamStatus;
    agoraChannelName: string;
    pinnedMessage?: {
        message: string;
        pinnedAt: Date;
        pinnedBy: string;
    };
    startTime: Date;
    endTime?: Date;
    viewerCount: number;
    filters?: {
        filterType: string;
        filterData: any;
    };
    maxViewers: number;
    totalComments: number;
    totalReactions: number;
    createdAt: Date;
    updatedAt: Date;
}

export const LiveStreamSchema: Schema = new Schema(
    {
        hostId: { 
            type: Schema.Types.ObjectId, 
            required: true, 
            ref: 'user', 
            index: 1 
        },
        title: { 
            type: String, 
            required: true, 
            maxlength: 100 
        },
        description: { 
            type: String, 
            maxlength: 500 
        },
        privacy: { 
            type: String, 
            enum: Object.values(LiveStreamPrivacy),
            default: LiveStreamPrivacy.Public 
        },
        allowedUsers: [{ 
            type: Schema.Types.ObjectId, 
            ref: 'user' 
        }],
        bannedUsers: [{ 
            type: Schema.Types.ObjectId, 
            ref: 'user' 
        }],
        status: { 
            type: String, 
            enum: Object.values(LiveStreamStatus),
            default: LiveStreamStatus.Active,
            index: 1
        },
        agoraChannelName: { 
            type: String, 
            required: true, 
            unique: true 
        },
        pinnedMessage: {
            message: { type: String },
            pinnedAt: { type: Date },
            pinnedBy: { type: Schema.Types.ObjectId, ref: 'user' }
        },
        startTime: { 
            type: Date, 
            default: Date.now,
            index: 1
        },
        endTime: { 
            type: Date 
        },
        viewerCount: { 
            type: Number, 
            default: 0 
        },
        filters: {
            filterType: { type: String },
            filterData: { type: Schema.Types.Mixed }
        },
        maxViewers: { 
            type: Number, 
            default: 0 
        },
        totalComments: { 
            type: Number, 
            default: 0 
        },
        totalReactions: { 
            type: Number, 
            default: 0 
        }
    },
    {
        timestamps: true,
    }
);

// Indexes for better performance
LiveStreamSchema.index({ hostId: 1, status: 1 });
LiveStreamSchema.index({ status: 1, startTime: -1 });
LiveStreamSchema.index({ privacy: 1, status: 1 });

// Add pagination plugin
LiveStreamSchema.plugin(aggregatePaginate);

// Auto-expire ended streams after 24 hours
LiveStreamSchema.index({ endTime: 1 }, { expireAfterSeconds: 86400 });
