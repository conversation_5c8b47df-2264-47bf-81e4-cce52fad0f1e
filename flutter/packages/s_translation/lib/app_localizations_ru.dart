// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class AppLocalizationsRu extends AppLocalizations {
  AppLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get done => 'Готово';

  @override
  String get loading => 'Загрузка...';

  @override
  String get messageHasBeenDeleted => 'Сообщение удалено';

  @override
  String get mute => 'Отключить';

  @override
  String get cancel => 'Отмена';

  @override
  String get typing => 'Печатает...';

  @override
  String get ok => 'OK';

  @override
  String get recording => 'Запись...';

  @override
  String get connecting => 'Подключение...';

  @override
  String get deleteYouCopy => 'Удалить вашу копию';

  @override
  String get unMute => 'Включить';

  @override
  String get delete => 'Удалить';

  @override
  String get report => 'Пожаловаться';

  @override
  String get leaveGroup => 'Покинуть группу';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      'Вы уверены, что хотите разрешить копирование? Это действие нельзя отменить';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      'Вы уверены, что хотите покинуть эту группу? Это действие нельзя отменить';

  @override
  String get leaveGroupAndDeleteYourMessageCopy =>
      'Покинуть группу и удалить вашу копию сообщения';

  @override
  String get vMessageInfoTrans => 'Информация о сообщении';

  @override
  String get updateTitleTo => 'Обновить заголовок на';

  @override
  String get updateImage => 'Обновить изображение';

  @override
  String get joinedBy => 'Присоединился';

  @override
  String get promotedToAdminBy => 'Повышен до администратора';

  @override
  String get dismissedToMemberBy => 'Понижен до участника';

  @override
  String get leftTheGroup => 'Покинул группу';

  @override
  String get you => 'Вы';

  @override
  String get kickedBy => 'Исключен';

  @override
  String get groupCreatedBy => 'Группу создал';

  @override
  String get groupDeletedBy => 'Group deleted by';

  @override
  String get addedYouToNewBroadcast => 'Добавил вас в новую рассылку';

  @override
  String get download => 'Скачать';

  @override
  String get copy => 'Копировать';

  @override
  String get info => 'Информация';

  @override
  String get share => 'Поделиться';

  @override
  String get forward => 'Переслать';

  @override
  String get reply => 'Ответить';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => 'Удалить для всех';

  @override
  String get deleteFromMe => 'Удалить для меня';

  @override
  String get downloading => 'Загрузка...';

  @override
  String get fileHasBeenSavedTo => 'Файл сохранен в';

  @override
  String get online => 'В сети';

  @override
  String get youDontHaveAccess => 'У вас нет доступа';

  @override
  String get replyToYourSelf => 'Ответить самому себе';

  @override
  String get repliedToYourSelf => 'Ответил самому себе';

  @override
  String get audioCall => 'Аудиозвонок';

  @override
  String get ring => 'Звонок';

  @override
  String get canceled => 'Отменено';

  @override
  String get timeout => 'Тайм-аут';

  @override
  String get rejected => 'Отклонено';

  @override
  String get finished => 'Завершено';

  @override
  String get inCall => 'В разговоре';

  @override
  String get sessionEnd => 'Конец сессии';

  @override
  String get yesterday => 'Вчера';

  @override
  String get today => 'Сегодня';

  @override
  String get textFieldHint => 'Введите сообщение...';

  @override
  String get files => 'Файлы';

  @override
  String get location => 'Местоположение';

  @override
  String get shareMediaAndLocation => 'Поделиться медиа и местоположением';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize =>
      'Есть видео больше разрешенного размера';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize =>
      'Есть файл больше разрешенного размера';

  @override
  String get makeCall => 'Сделать звонок';

  @override
  String get areYouWantToMakeVideoCall => 'Вы хотите сделать видеозвонок?';

  @override
  String get areYouWantToMakeVoiceCall => 'Вы хотите сделать голосовой звонок?';

  @override
  String get vMessagesInfoTrans => 'Информация о сообщениях';

  @override
  String get star => 'Звезда';

  @override
  String get minutes => 'Минуты';

  @override
  String get sendMessage => 'Отправить сообщение';

  @override
  String get deleteUser => 'Удалить пользователя';

  @override
  String get actions => 'Действия';

  @override
  String get youAreAboutToDeleteThisUserFromYourList =>
      'Вы собираетесь удалить этого пользователя из вашего списка';

  @override
  String get updateBroadcastTitle => 'Обновить заголовок рассылки';

  @override
  String get usersAddedSuccessfully => 'Пользователи успешно добавлены';

  @override
  String get broadcastSettings => 'Настройки рассылки';

  @override
  String get addParticipants => 'Добавить участников';

  @override
  String get broadcastParticipants => 'Участники рассылки';

  @override
  String get updateGroupDescription => 'Обновить описание группы';

  @override
  String get updateGroupTitle => 'Обновить название группы';

  @override
  String get groupSettings => 'Настройки группы';

  @override
  String get description => 'Описание';

  @override
  String get muteNotifications => 'Отключить уведомления';

  @override
  String get groupParticipants => 'Участники группы';

  @override
  String get blockUser => 'Заблокировать пользователя';

  @override
  String get areYouSureToBlock => 'Вы уверены, что хотите заблокировать';

  @override
  String get userPage => 'Страница пользователя';

  @override
  String get starMessage => 'Звездное сообщение';

  @override
  String get showMedia => 'Показать медиа';

  @override
  String get reportUser => 'Пожаловаться на пользователя';

  @override
  String get groupName => 'Имя группы';

  @override
  String get changeSubject => 'Изменить тему';

  @override
  String get titleIsRequired => 'Заголовок обязателен';

  @override
  String get createBroadcast => 'Создать рассылку';

  @override
  String get broadcastName => 'Имя рассылки';

  @override
  String get createGroup => 'Создать группу';

  @override
  String get forgetPassword => 'Забыли пароль';

  @override
  String get globalSearch => 'Глобальный поиск';

  @override
  String get dismissesToMember => 'Уволить до участника';

  @override
  String get setToAdmin => 'Установить администратором';

  @override
  String get kickMember => 'Исключить участника';

  @override
  String get youAreAboutToDismissesToMember =>
      'Вы собираетесь уволить участника';

  @override
  String get youAreAboutToKick => 'Вы собираетесь исключить';

  @override
  String get groupMembers => 'Участники группы';

  @override
  String get tapForPhoto => 'Коснитесь для фото';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      'Мы настоятельно рекомендуем скачать это обновление';

  @override
  String get newGroup => 'Новая группа';

  @override
  String get newBroadcast => 'Новая рассылка';

  @override
  String get starredMessage => 'Помеченное сообщение';

  @override
  String get settings => 'Настройки';

  @override
  String get chats => 'ЧАТЫ';

  @override
  String get recentUpdates => 'Последние обновления';

  @override
  String get startChat => 'Начать чат';

  @override
  String get newUpdateIsAvailable => 'Доступно новое обновление';

  @override
  String get emailNotValid => 'Email недействителен';

  @override
  String get passwordMustHaveValue => 'Пароль должен иметь значение';

  @override
  String get error => 'Ошибка';

  @override
  String get password => 'Пароль';

  @override
  String get login => 'Вход';

  @override
  String get needNewAccount => 'Нужен новый аккаунт?';

  @override
  String get register => 'Зарегистрироваться';

  @override
  String get nameMustHaveValue => 'Имя должно иметь значение';

  @override
  String get passwordNotMatch => 'Пароли не совпадают';

  @override
  String get name => 'Имя';

  @override
  String get email => 'Email';

  @override
  String get confirmPassword => 'Подтвердите пароль';

  @override
  String get alreadyHaveAnAccount => 'Уже есть аккаунт?';

  @override
  String get logOut => 'Выйти';

  @override
  String get back => 'Назад';

  @override
  String get sendCodeToMyEmail => 'Отправить код на мой email';

  @override
  String get invalidLoginData => 'Неверные данные для входа';

  @override
  String get userEmailNotFound => 'Email пользователя не найден';

  @override
  String get yourAccountBlocked => 'Ваш аккаунт заблокирован';

  @override
  String get yourAccountDeleted => 'Ваш аккаунт удален';

  @override
  String get userAlreadyRegister => 'Пользователь уже зарегистрирован';

  @override
  String get codeHasBeenExpired => 'Срок действия кода истек';

  @override
  String get invalidCode => 'Неверный код';

  @override
  String get whileAuthCanFindYou =>
      'Во время аутентификации не удалось найти вас';

  @override
  String get userRegisterStatusNotAcceptedYet =>
      'Статус регистрации пользователя еще не принят';

  @override
  String get deviceHasBeenLogoutFromAllDevices =>
      'Устройство было выведено из системы со всех устройств';

  @override
  String get userDeviceSessionEndDeviceDeleted =>
      'Сеанс устройства пользователя завершен, устройство удалено';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      'На ваш email не отправлен код для подтверждения';

  @override
  String get roomAlreadyInCall => 'Комната уже в разговоре';

  @override
  String get peerUserInCallNow => 'Пользователь сейчас в разговоре';

  @override
  String get callNotAllowed => 'Звонок не разрешен';

  @override
  String get peerUserDeviceOffline => 'Устройство пользователя недоступно';

  @override
  String get emailMustBeValid => 'Email должен быть действительным';

  @override
  String get wait2MinutesToSendMail =>
      'Подождите 2 минуты, чтобы отправить письмо';

  @override
  String get codeMustEqualToSixNumbers => 'Код должен состоять из шести цифр';

  @override
  String get newPasswordMustHaveValue => 'Новый пароль должен иметь значение';

  @override
  String get confirmPasswordMustHaveValue =>
      'Подтверждение пароля должно иметь значение';

  @override
  String get congregationsYourAccountHasBeenAccepted =>
      'Поздравляем, ваш аккаунт принят';

  @override
  String get yourAccountIsUnderReview =>
      'Ваш аккаунт находится на рассмотрении';

  @override
  String get waitingList => 'Очередь ожидания';

  @override
  String get welcome => 'Добро пожаловать';

  @override
  String get retry => 'Повторить';

  @override
  String get deleteMember => 'Удалить участника';

  @override
  String get profile => 'Профиль';

  @override
  String get broadcastInfo => 'Информация о рассылке';

  @override
  String get updateTitle => 'Обновить заголовок';

  @override
  String get members => 'Участники';

  @override
  String get addMembers => 'Добавить участников';

  @override
  String get success => 'Успешно';

  @override
  String get media => 'Медиа';

  @override
  String get docs => 'Документы';

  @override
  String get links => 'Ссылки';

  @override
  String get soon => 'Скоро';

  @override
  String get unStar => 'Убрать звезду';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      'Обновление описания группы затронет всех участников группы';

  @override
  String get updateNickname => 'Обновить никнейм';

  @override
  String get groupInfo => 'Информация о группе';

  @override
  String get youNotParticipantInThisGroup => 'Вы не участник этой группы';

  @override
  String get search => 'Поиск';

  @override
  String get mediaLinksAndDocs => 'Медиа, Ссылки и Документы';

  @override
  String get starredMessages => 'Помеченные сообщения';

  @override
  String get nickname => 'Никнейм';

  @override
  String get none => 'Нет';

  @override
  String get yes => 'Да';

  @override
  String get no => 'Нет';

  @override
  String get exitGroup => 'Покинуть группу';

  @override
  String get clickToAddGroupDescription =>
      'Нажмите, чтобы добавить описание группы';

  @override
  String get unBlockUser => 'Разблокировать пользователя';

  @override
  String get areYouSureToUnBlock => 'Вы уверены, что хотите разблокировать';

  @override
  String get contactInfo => 'Контактная информация';

  @override
  String get audio => 'Аудио';

  @override
  String get video => 'Видео';

  @override
  String get hiIamUse => 'Привет, я использую';

  @override
  String get on => 'Вкл';

  @override
  String get off => 'Выкл';

  @override
  String get unBlock => 'Разблокировать';

  @override
  String get block => 'Блокировать';

  @override
  String get chooseAtLestOneMember => 'Выберите хотя бы одного участника';

  @override
  String get close => 'Закрыть';

  @override
  String get next => 'Далее';

  @override
  String get appMembers => 'Участники приложения';

  @override
  String get create => 'Создать';

  @override
  String get upgradeToAdmin => 'Повысить до администратора';

  @override
  String get update => 'Обновить';

  @override
  String get deleteChat => 'Удалить чат';

  @override
  String get clearChat => 'Очистить чат';

  @override
  String get showHistory => 'Показать историю';

  @override
  String get groupIcon => 'Иконка группы';

  @override
  String get tapToSelectAnIcon => 'Коснитесь, чтобы выбрать иконку';

  @override
  String get groupDescription => 'Описание группы';

  @override
  String get more => 'Еще';

  @override
  String get messageInfo => 'Информация о сообщении';

  @override
  String get successfullyDownloadedIn => 'Успешно скачано в';

  @override
  String get delivered => 'Доставлено';

  @override
  String get read => 'Прочитано';

  @override
  String get orLoginWith => 'или войдите с помощью';

  @override
  String get resetPassword => 'Сброс пароля';

  @override
  String get otpCode => 'OTP-код';

  @override
  String get newPassword => 'Новый пароль';

  @override
  String get areYouSure => 'Вы уверены?';

  @override
  String get broadcastMembers => 'Участники рассылки';

  @override
  String get phone => 'Телефон';

  @override
  String get users => 'Пользователи';

  @override
  String get calls => 'Звонки';

  @override
  String get yourAreAboutToLogoutFromThisAccount =>
      'Вы собираетесь выйти из этой учетной записи';

  @override
  String get noUpdatesAvailableNow => 'Сейчас нет доступных обновлений';

  @override
  String get dataPrivacy => 'Защита данных';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      'Все данные были сохранены, вам не нужно управлять сохранением данных самостоятельно! Если вы выйдете и войдете в систему снова, вы увидите все чаты такие же, как в веб-версии';

  @override
  String get account => 'Учетная запись';

  @override
  String get linkedDevices => 'Подключенные устройства';

  @override
  String get storageAndData => 'Хранилище и данные';

  @override
  String get tellAFriend => 'Расскажи другу';

  @override
  String get help => 'Помощь';

  @override
  String get blockedUsers => 'Заблокированные пользователи';

  @override
  String get inAppAlerts => 'Оповещения в приложении';

  @override
  String get language => 'Язык';

  @override
  String get adminNotification => 'Уведомление администратора';

  @override
  String get checkForUpdates => 'Проверить обновления';

  @override
  String get linkByQrCode => 'Ссылка по QR-коду';

  @override
  String get deviceStatus => 'Состояние устройства';

  @override
  String get desktopAndOtherDevices => 'ПК и другие устройства';

  @override
  String get linkADeviceSoon => 'Скоро подключить устройство';

  @override
  String get lastActiveFrom => 'Последняя активность с';

  @override
  String get tapADeviceToEditOrLogOut =>
      'Коснитесь устройства, чтобы отредактировать или выйти.';

  @override
  String get contactUs => 'Свяжитесь с нами';

  @override
  String get supportChatSoon => 'Чат поддержки (скоро)';

  @override
  String get updateYourName => 'Обновите свое имя';

  @override
  String get updateYourBio => 'Обновите свою биографию';

  @override
  String get edit => 'Редактировать';

  @override
  String get about => 'О приложении';

  @override
  String get oldPassword => 'Старый пароль';

  @override
  String get deleteMyAccount => 'Удалить мой аккаунт';

  @override
  String get passwordHasBeenChanged => 'Пароль был изменен';

  @override
  String get logoutFromAllDevices => 'Выйти со всех устройств?';

  @override
  String get updateYourPassword => 'Обновите свой пароль';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      'Введите имя и добавьте необязательное фото профиля';

  @override
  String get privacyPolicy => 'Политика конфиденциальности';

  @override
  String get chat => 'Чат';

  @override
  String get send => 'Отправить';

  @override
  String get reportHasBeenSubmitted => 'Ваше сообщение было отправлено';

  @override
  String get offline => 'Офлайн';

  @override
  String get harassmentOrBullyingDescription =>
      'Домогательство или запугивание: эта опция позволяет пользователям сообщать о лицах, нацеленных на них или других, с помощью домогательных сообщений, угроз или других форм запугивания.';

  @override
  String get spamOrScamDescription =>
      'Спам или мошенничество: с помощью этой опции пользователи могут сообщать о счетах, которые отправляют спам-сообщения, нежелательную рекламу или пытаются обмануть других.';

  @override
  String get areYouSureToReportUserToAdmin =>
      'Вы уверены, что хотите отправить жалобу об этом пользователе администратору?';

  @override
  String get groupWith => 'Группа с';

  @override
  String get inappropriateContentDescription =>
      'Неуместное содержание: Пользователи могут выбрать эту опцию, чтобы сообщить о любом сексуально откровенном материале, ненавистнической речи, или другом контенте, который нарушает стандарты сообщества.';

  @override
  String get otherCategoryDescription =>
      'Другое: Эта общая категория может использоваться для нарушений, которые не легко подпадают под вышеуказанные категории. Может быть полезно включить текстовое поле, чтобы пользователи могли предоставить дополнительные сведения.';

  @override
  String get explainWhatHappens => 'Объясните здесь, что происходит';

  @override
  String get loginAgain => 'Войдите снова!';

  @override
  String get yourSessionIsEndedPleaseLoginAgain =>
      'Ваша сессия завершена, пожалуйста, войдите снова!';

  @override
  String get aboutToBlockUserWithConsequences =>
      'Вы собираетесь заблокировать этого пользователя. Вы не сможете отправлять ему чаты и не сможете добавлять его в группы или рассылки!';

  @override
  String get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
      'Вы собираетесь удалить свою учетную запись, и ваша учетная запись больше не будет отображаться в списке пользователей';

  @override
  String get admin => 'Админ';

  @override
  String get member => 'Участник';

  @override
  String get creator => 'Создатель';

  @override
  String get currentDevice => 'Текущее устройство';

  @override
  String get visits => 'Посещения';

  @override
  String get chooseRoom => 'Выберите комнату';

  @override
  String get deleteThisDeviceDesc =>
      'Удаление этого устройства приведет к немедленному выходу из учетной записи на этом устройстве';

  @override
  String get youAreAboutToUpgradeToAdmin =>
      'Вы собираетесь повысить до администратора';

  @override
  String get microphonePermissionMustBeAccepted =>
      'Microphone permission must be accepted';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'Microphone and camera permission must be accepted';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      'Вход разрешен. Пожалуйста, попробуйте позже.';

  @override
  String get dashboard => 'Панель';

  @override
  String get notification => 'Уведомление';

  @override
  String get total => 'Всего';

  @override
  String get blocked => 'Заблокировано';

  @override
  String get deleted => 'Удалено';

  @override
  String get accepted => 'Принято';

  @override
  String get notAccepted => 'Не принято';

  @override
  String get web => 'Веб';

  @override
  String get android => 'Android';

  @override
  String get macOs => 'macOS';

  @override
  String get windows => 'Windows';

  @override
  String get other => 'Другое';

  @override
  String get totalVisits => 'Всего посещений';

  @override
  String get totalMessages => 'Всего сообщений';

  @override
  String get textMessages => 'Текстовые сообщения';

  @override
  String get imageMessages => 'Изображения';

  @override
  String get videoMessages => 'Видео сообщения';

  @override
  String get voiceMessages => 'Голосовые сообщения';

  @override
  String get fileMessages => 'Файлы';

  @override
  String get infoMessages => 'Информационные сообщения';

  @override
  String get voiceCallMessages => 'Голосовые вызовы';

  @override
  String get videoCallMessages => 'Видеозвонки';

  @override
  String get locationMessages => 'Местоположение';

  @override
  String get directChat => 'Личный чат';

  @override
  String get group => 'Группа';

  @override
  String get broadcast => 'Трансляция';

  @override
  String get messageCounter => 'Счетчик сообщений';

  @override
  String get roomCounter => 'Счетчик комнат';

  @override
  String get countries => 'Страны';

  @override
  String get devices => 'Устройства';

  @override
  String get notificationTitle => 'Заголовок уведомления';

  @override
  String get notificationDescription => 'Описание уведомления';

  @override
  String get notificationsPage => 'Страница уведомлений';

  @override
  String get updateFeedBackEmail =>
      'Обновить адрес электронной почты для обратной связи';

  @override
  String get setMaxMessageForwardAndShare =>
      'Установить максимальное количество сообщений для пересылки и обмена';

  @override
  String get setNewPrivacyPolicyUrl =>
      'Установить новый URL политики конфиденциальности';

  @override
  String get forgetPasswordExpireTime => 'Время действия сброса пароля';

  @override
  String get callTimeoutInSeconds => 'Тайм-аут вызова (в секундах)';

  @override
  String get setMaxGroupMembers =>
      'Установить максимальное количество участников группы';

  @override
  String get setMaxBroadcastMembers =>
      'Установить максимальное количество участников трансляции';

  @override
  String get allowCalls => 'Разрешить вызовы';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      'Если эта опция включена, разрешены видео и голосовые вызовы';

  @override
  String get allowAds => 'Разрешить рекламу';

  @override
  String get allowMobileLogin => 'Разрешить мобильный вход';

  @override
  String get allowWebLogin => 'Разрешить вход через веб';

  @override
  String get messages => 'Сообщения';

  @override
  String get appleStoreAppUrl => 'URL приложения в App Store';

  @override
  String get googlePlayAppUrl => 'URL приложения в Google Play';

  @override
  String get privacyUrl => 'URL политики конфиденциальности';

  @override
  String get feedBackEmail => 'Email обратной связи';

  @override
  String get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
      'Если эта опция отключена, будет заблокирована отправка файлов чата, изображений, видео и местоположения';

  @override
  String get allowSendMedia => 'Разрешить отправку медиафайлов';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      'Если эта опция отключена, будет заблокировано создание трансляций чата';

  @override
  String get allowCreateBroadcast => 'Разрешить создание трансляций';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      'Если эта опция отключена, будет заблокировано создание групповых чатов';

  @override
  String get allowCreateGroups => 'Разрешить создание групп';

  @override
  String get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
      'Если эта опция отключена, будет заблокирован вход или регистрация на рабочем столе (Windows и macOS)';

  @override
  String get allowDesktopLogin => 'Разрешить вход с рабочего стола';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      'Если эта опция отключена, будет заблокирован вход или регистрация через веб';

  @override
  String
      get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
          'Если эта опция включена, будет показываться баннер Google Ads в чатах';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'If this option is enabled, the Google Ads banner will appear in chats.';

  @override
  String get userProfile => 'Профиль пользователя';

  @override
  String get userInfo => 'Информация о пользователе';

  @override
  String get fullName => 'Полное имя';

  @override
  String get bio => 'Биография';

  @override
  String get noBio => 'Нет биографии';

  @override
  String get verifiedAt => 'Подтверждено';

  @override
  String get country => 'Страна';

  @override
  String get registerStatus => 'Статус регистрации';

  @override
  String get registerMethod => 'Метод регистрации';

  @override
  String get banTo => 'Заблокирован до';

  @override
  String get deletedAt => 'Удалено в';

  @override
  String get createdAt => 'Создано в';

  @override
  String get updatedAt => 'Обновлено в';

  @override
  String get reports => 'Отчеты';

  @override
  String get clickToSeeAllUserDevicesDetails =>
      'Нажмите, чтобы посмотреть все сведения об устройствах пользователя';

  @override
  String get allDeletedMessages => 'Все удаленные сообщения';

  @override
  String get voiceCallMessage => 'Голосовое сообщение';

  @override
  String get totalRooms => 'Всего комнат';

  @override
  String get directRooms => 'Прямые комнаты';

  @override
  String get userAction => 'Действия пользователя';

  @override
  String get status => 'Статус';

  @override
  String get joinedAt => 'Присоединился в';

  @override
  String get saveLogin => 'Сохранить вход';

  @override
  String get passwordIsRequired => 'Пароль обязателен';

  @override
  String get verified => 'Подтверждено';

  @override
  String get pending => 'Ожидает';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => 'Описание обязательно';

  @override
  String get seconds => 'секунд';

  @override
  String get clickToSeeAllUserInformations =>
      'Нажмите, чтобы посмотреть все сведения о пользователе';

  @override
  String get clickToSeeAllUserCountries =>
      'Нажмите, чтобы посмотреть все страны пользователя';

  @override
  String get clickToSeeAllUserMessagesDetails =>
      'Нажмите, чтобы посмотреть все сведения о сообщениях пользователя';

  @override
  String get clickToSeeAllUserRoomsDetails =>
      'Нажмите, чтобы посмотреть все сведения о комнатах пользователя';

  @override
  String get clickToSeeAllUserReports =>
      'Нажмите, чтобы посмотреть все отчеты пользователя';

  @override
  String get banAt => 'Заблокирован в';

  @override
  String get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
      'Now you login as read-only admin. All edits you make will not be applied due to this being a test version.';

  @override
  String get createStory => 'Создать историю';

  @override
  String get writeACaption => 'Напишите подпись...';

  @override
  String get storyCreatedSuccessfully => 'История успешно создана';

  @override
  String get stories => 'Истории';

  @override
  String get clear => 'Очистить';

  @override
  String get clearCallsConfirm => 'Подтвердить очистку звонков';

  @override
  String get chooseHowAutomaticDownloadWorks =>
      'Выберите, как работает автоматическая загрузка';

  @override
  String get whenUsingMobileData => 'При использовании мобильных данных';

  @override
  String get whenUsingWifi => 'При использовании Wi-Fi';

  @override
  String get image => 'Изображение';

  @override
  String get myPrivacy => 'Моя конфиденциальность';

  @override
  String get createTextStory => 'Создать текстовую историю';

  @override
  String get createMediaStory => 'Создать медиа-историю';

  @override
  String get camera => 'Камера';

  @override
  String get gallery => 'Галерея';

  @override
  String get recentUpdate => 'Недавнее обновление';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => 'Добавить новую историю';

  @override
  String get updateYourProfile => 'Обновите свой профиль';

  @override
  String get configureYourAccountPrivacy =>
      'Настройте конфиденциальность аккаунта';

  @override
  String get youInPublicSearch => 'Вы в публичном поиске';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      'Ваш профиль отображается в публичном поиске и добавлении в группы';

  @override
  String get yourLastSeen => 'В последний раз видели';

  @override
  String get yourLastSeenInChats => 'В последний раз видели в чатах';

  @override
  String get startNewChatWithYou => 'Начать новый чат с вами';

  @override
  String get yourStory => 'Ваша история';

  @override
  String get forRequest => 'Для запроса';

  @override
  String get public => 'Общественный';

  @override
  String get createYourStory => 'Создайте свою историю';

  @override
  String get shareYourStatus => 'Поделитесь своим статусом';

  @override
  String get oneSeenMessage => 'Одно просмотренное сообщение';

  @override
  String get messageHasBeenViewed => 'Сообщение просмотрено';

  @override
  String get clickToSee => 'Нажмите, чтобы увидеть';

  @override
  String get images => 'Изображения';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String get areYouSureRemoveAccount =>
      'Are you sure you want to remove this account?';

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';

  @override
  String get accountAddedSuccessfully => 'Account added successfully';

  @override
  String get addProfilePicture => 'Add Profile Picture';

  @override
  String get addProfilePictureSubtitle =>
      'Add a profile picture to help others recognize you';

  @override
  String get pleaseSelectProfilePicture => 'Please select a profile picture';

  @override
  String get uploading => 'Uploading...';

  @override
  String get continueText => 'Continue';

  @override
  String get profilePictureRequired =>
      'A profile picture is required to continue. Please select an image to upload.';

  @override
  String get shareProfile => 'Share Profile';

  @override
  String get liveStreams => 'Live Streams';

  @override
  String get goLive => 'Go Live';

  @override
  String get shareYourMomentsLive => 'Share your moments live with friends';

  @override
  String get viewers => 'viewers';

  @override
  String get join => 'Join';

  @override
  String get joinLiveStream => 'Join Live Stream';

  @override
  String get viewDetails => 'View Details';

  @override
  String get startLiveStream => 'Start Live Stream';

  @override
  String get endLiveStream => 'End Live Stream';

  @override
  String get leaveLiveStream => 'Leave Live Stream';

  @override
  String get inviteUsers => 'Invite Users';

  @override
  String get banUser => 'Ban User';

  @override
  String get unbanUser => 'Unban User';

  @override
  String get pinMessage => 'Pin Message';

  @override
  String get unpinMessage => 'Unpin Message';

  @override
  String get sendComment => 'Send Comment';

  @override
  String get addReaction => 'Add Reaction';

  @override
  String get liveStreamTitle => 'Live Stream Title';

  @override
  String get liveStreamDescription => 'Description (optional)';

  @override
  String get whoCanWatch => 'Who can watch?';

  @override
  String get everyone => 'Everyone';

  @override
  String get specificFriends => 'Specific Friends';

  @override
  String get selectFriends => 'Select Friends';

  @override
  String get applyFilters => 'Apply Filters';

  @override
  String get noFilters => 'No Filters';

  @override
  String get startStreaming => 'Start Streaming';

  @override
  String get streamEnded => 'Stream Ended';

  @override
  String get youAreNowLive => 'You are now live!';

  @override
  String get streamEndedByHost => 'Stream ended by host';

  @override
  String get removedFromStream => 'You have been removed from the stream';

  @override
  String get commentPlaceholder => 'Add a comment...';

  @override
  String get pinnedMessage => 'Pinned Message';

  @override
  String get hostControls => 'Host Controls';

  @override
  String get participants => 'Participants';

  @override
  String get comments => 'Comments';

  @override
  String get reactions => 'Reactions';
}
