// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Vietnamese (`vi`).
class AppLocalizationsVi extends AppLocalizations {
  AppLocalizationsVi([String locale = 'vi']) : super(locale);

  @override
  String get done => 'Xong';

  @override
  String get loading => 'Đang tải ...';

  @override
  String get messageHasBeenDeleted => 'Tin nhắn đã bị xóa';

  @override
  String get mute => 'Tắt thông báo';

  @override
  String get cancel => 'Hủy';

  @override
  String get typing => 'Đang nhập...';

  @override
  String get ok => 'OK';

  @override
  String get recording => 'Đang ghi âm...';

  @override
  String get connecting => 'Đang kết nối...';

  @override
  String get deleteYouCopy => 'Xóa bản sao của bạn';

  @override
  String get unMute => 'Bật thông báo';

  @override
  String get delete => 'Xóa';

  @override
  String get report => 'Báo cáo';

  @override
  String get leaveGroup => 'Rời nhóm';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      'Bạn có chắc chắn muốn cho phép bản sao của bạn? Hành động này không thể hoàn tác';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      'Bạn có chắc chắn muốn rời khỏi nhóm này? Hành động này không thể hoàn tác';

  @override
  String get leaveGroupAndDeleteYourMessageCopy =>
      'Rời khỏi nhóm và xóa bản sao tin nhắn của bạn';

  @override
  String get vMessageInfoTrans => 'Thông tin tin nhắn';

  @override
  String get updateTitleTo => 'Cập nhật tiêu đề thành';

  @override
  String get updateImage => 'Cập nhật ảnh';

  @override
  String get joinedBy => 'Tham gia bởi';

  @override
  String get promotedToAdminBy => 'Được thăng cấp thành quản trị viên bởi';

  @override
  String get dismissedToMemberBy => 'Bị giáng chức thành thành viên bởi';

  @override
  String get leftTheGroup => 'Rời khỏi nhóm';

  @override
  String get you => 'Bạn';

  @override
  String get kickedBy => 'Bị đá ra khỏi nhóm bởi';

  @override
  String get groupCreatedBy => 'Nhóm được tạo bởi';

  @override
  String get groupDeletedBy => 'Group deleted by';

  @override
  String get addedYouToNewBroadcast => 'Đã thêm bạn vào phát sóng mới';

  @override
  String get download => 'Tải xuống';

  @override
  String get copy => 'Sao chép';

  @override
  String get info => 'Thông tin';

  @override
  String get share => 'Chia sẻ';

  @override
  String get forward => 'Chuyển tiếp';

  @override
  String get reply => 'Trả lời';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => 'Xóa khỏi tất cả';

  @override
  String get deleteFromMe => 'Xóa khỏi tôi';

  @override
  String get downloading => 'Đang tải xuống...';

  @override
  String get fileHasBeenSavedTo => 'Tệp đã được lưu tại';

  @override
  String get online => 'Trực tuyến';

  @override
  String get youDontHaveAccess => 'Bạn không có quyền truy cập';

  @override
  String get replyToYourSelf => 'Trả lời cho chính bạn';

  @override
  String get repliedToYourSelf => 'Đã trả lời cho chính bạn';

  @override
  String get audioCall => 'Cuộc gọi âm thanh';

  @override
  String get ring => 'Nhạc chuông';

  @override
  String get canceled => 'Đã hủy';

  @override
  String get timeout => 'Hết thời gian';

  @override
  String get rejected => 'Đã từ chối';

  @override
  String get finished => 'Hoàn thành';

  @override
  String get inCall => 'Trong cuộc gọi';

  @override
  String get sessionEnd => 'Kết thúc phiên';

  @override
  String get yesterday => 'Hôm qua';

  @override
  String get today => 'Hôm nay';

  @override
  String get textFieldHint => 'Nhập tin nhắn ...';

  @override
  String get files => 'Tệp';

  @override
  String get location => 'Vị trí';

  @override
  String get shareMediaAndLocation => 'Chia sẻ phương tiện và vị trí';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize =>
      'Có video có kích thước lớn hơn kích thước cho phép';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize =>
      'Có tệp có kích thước lớn hơn kích thước cho phép';

  @override
  String get makeCall => 'Thực hiện cuộc gọi';

  @override
  String get areYouWantToMakeVideoCall =>
      'Bạn có muốn thực hiện cuộc gọi video không?';

  @override
  String get areYouWantToMakeVoiceCall =>
      'Bạn có muốn thực hiện cuộc gọi thoại không?';

  @override
  String get vMessagesInfoTrans => 'Thông tin tin nhắn';

  @override
  String get star => 'Đánh dấu';

  @override
  String get minutes => 'Phút';

  @override
  String get sendMessage => 'Gửi tin nhắn';

  @override
  String get deleteUser => 'Xóa người dùng';

  @override
  String get actions => 'Hành động';

  @override
  String get youAreAboutToDeleteThisUserFromYourList =>
      'Bạn đang chuẩn bị xóa người dùng này khỏi danh sách của bạn';

  @override
  String get updateBroadcastTitle => 'Cập nhật tiêu đề phát sóng';

  @override
  String get usersAddedSuccessfully => 'Người dùng được thêm thành công';

  @override
  String get broadcastSettings => 'Cài đặt phát sóng';

  @override
  String get addParticipants => 'Thêm người tham gia';

  @override
  String get broadcastParticipants => 'Người tham gia phát sóng';

  @override
  String get updateGroupDescription => 'Cập nhật mô tả nhóm';

  @override
  String get updateGroupTitle => 'Cập nhật tiêu đề nhóm';

  @override
  String get groupSettings => 'Cài đặt nhóm';

  @override
  String get description => 'Mô tả';

  @override
  String get muteNotifications => 'Tắt thông báo';

  @override
  String get groupParticipants => 'Người tham gia nhóm';

  @override
  String get blockUser => 'Chặn người dùng';

  @override
  String get areYouSureToBlock => 'Bạn có chắc chắn muốn chặn không';

  @override
  String get userPage => 'Trang người dùng';

  @override
  String get starMessage => 'Đánh dấu tin nhắn';

  @override
  String get showMedia => 'Hiển thị phương tiện';

  @override
  String get reportUser => 'Báo cáo người dùng';

  @override
  String get groupName => 'Tên nhóm';

  @override
  String get changeSubject => 'Thay đổi chủ đề';

  @override
  String get titleIsRequired => 'Tiêu đề là bắt buộc';

  @override
  String get createBroadcast => 'Tạo phát sóng';

  @override
  String get broadcastName => 'Tên phát sóng';

  @override
  String get createGroup => 'Tạo nhóm';

  @override
  String get forgetPassword => 'Quên mật khẩu';

  @override
  String get globalSearch => 'Tìm kiếm toàn cầu';

  @override
  String get dismissesToMember => 'Giáng chức thành thành viên';

  @override
  String get setToAdmin => 'Thiết lập là quản trị viên';

  @override
  String get kickMember => 'Đá ra khỏi nhóm';

  @override
  String get youAreAboutToDismissesToMember =>
      'Bạn đang chuẩn bị giáng chức thành viên';

  @override
  String get youAreAboutToKick => 'Bạn đang chuẩn bị đá ra khỏi nhóm';

  @override
  String get groupMembers => 'Thành viên nhóm';

  @override
  String get tapForPhoto => 'Chạm để chụp ảnh';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      'Chúng tôi rất khuyến nghị tải xuống bản cập nhật này';

  @override
  String get newGroup => 'Nhóm mới';

  @override
  String get newBroadcast => 'Phát sóng mới';

  @override
  String get starredMessage => 'Tin nhắn được đánh dấu';

  @override
  String get settings => 'Cài đặt';

  @override
  String get chats => 'CUỘC TRÒ CHUYỆN';

  @override
  String get recentUpdates => 'Cập nhật gần đây';

  @override
  String get startChat => 'Bắt đầu trò chuyện';

  @override
  String get newUpdateIsAvailable => 'Có bản cập nhật mới';

  @override
  String get emailNotValid => 'Email không hợp lệ';

  @override
  String get passwordMustHaveValue => 'Mật khẩu phải có giá trị';

  @override
  String get error => 'Lỗi';

  @override
  String get password => 'Mật khẩu';

  @override
  String get login => 'Đăng nhập';

  @override
  String get needNewAccount => 'Cần tạo tài khoản mới?';

  @override
  String get register => 'Đăng ký';

  @override
  String get nameMustHaveValue => 'Tên phải có giá trị';

  @override
  String get passwordNotMatch => 'Mật khẩu không khớp';

  @override
  String get name => 'Tên';

  @override
  String get email => 'Email';

  @override
  String get confirmPassword => 'Xác nhận mật khẩu';

  @override
  String get alreadyHaveAnAccount => 'Đã có tài khoản?';

  @override
  String get logOut => 'Đăng xuất';

  @override
  String get back => 'Quay lại';

  @override
  String get sendCodeToMyEmail => 'Gửi mã đến email của tôi';

  @override
  String get invalidLoginData => 'Dữ liệu đăng nhập không hợp lệ';

  @override
  String get userEmailNotFound => 'Không tìm thấy email người dùng';

  @override
  String get yourAccountBlocked => 'Tài khoản của bạn đã bị chặn';

  @override
  String get yourAccountDeleted => 'Tài khoản của bạn đã bị xóa';

  @override
  String get userAlreadyRegister => 'Người dùng đã đăng ký';

  @override
  String get codeHasBeenExpired => 'Mã đã hết hạn';

  @override
  String get invalidCode => 'Mã không hợp lệ';

  @override
  String get whileAuthCanFindYou =>
      'Trong quá trình xác thực không thể tìm thấy bạn';

  @override
  String get userRegisterStatusNotAcceptedYet =>
      'Trạng thái đăng ký của người dùng chưa được chấp nhận';

  @override
  String get deviceHasBeenLogoutFromAllDevices =>
      'Thiết bị đã đăng xuất khỏi tất cả các thiết bị';

  @override
  String get userDeviceSessionEndDeviceDeleted =>
      'Phiên thiết bị người dùng đã kết thúc - Thiết bị đã bị xóa';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      'Không có mã đã được gửi cho bạn để xác minh email của bạn';

  @override
  String get roomAlreadyInCall => 'Phòng đã trong cuộc gọi';

  @override
  String get peerUserInCallNow => 'Người dùng đang trong cuộc gọi';

  @override
  String get callNotAllowed => 'Cuộc gọi không được phép';

  @override
  String get peerUserDeviceOffline =>
      'Thiết bị của người dùng đối tác ngoại tuyến';

  @override
  String get emailMustBeValid => 'Email phải hợp lệ';

  @override
  String get wait2MinutesToSendMail => 'Chờ 2 phút để gửi mail';

  @override
  String get codeMustEqualToSixNumbers => 'Mã phải bằng sáu chữ số';

  @override
  String get newPasswordMustHaveValue => 'Mật khẩu mới phải có giá trị';

  @override
  String get confirmPasswordMustHaveValue =>
      'Xác nhận mật khẩu phải có giá trị';

  @override
  String get congregationsYourAccountHasBeenAccepted =>
      'Chúc mừng tài khoản của bạn đã được chấp nhận';

  @override
  String get yourAccountIsUnderReview => 'Tài khoản của bạn đang được xem xét';

  @override
  String get waitingList => 'Danh sách chờ';

  @override
  String get welcome => 'Chào mừng';

  @override
  String get retry => 'Thử lại';

  @override
  String get deleteMember => 'Xóa thành viên';

  @override
  String get profile => 'Hồ sơ';

  @override
  String get broadcastInfo => 'Thông tin phát sóng';

  @override
  String get updateTitle => 'Cập nhật tiêu đề';

  @override
  String get members => 'Thành viên';

  @override
  String get addMembers => 'Thêm thành viên';

  @override
  String get success => 'Thành công';

  @override
  String get media => 'Phương tiện';

  @override
  String get docs => 'Tài liệu';

  @override
  String get links => 'Liên kết';

  @override
  String get soon => 'Sớm';

  @override
  String get unStar => 'Bỏ đánh dấu';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      'Cập nhật mô tả nhóm sẽ cập nhật cho tất cả thành viên nhóm';

  @override
  String get updateNickname => 'Cập nhật biệt danh';

  @override
  String get groupInfo => 'Thông tin nhóm';

  @override
  String get youNotParticipantInThisGroup =>
      'Bạn không phải là thành viên trong nhóm này';

  @override
  String get search => 'Tìm kiếm';

  @override
  String get mediaLinksAndDocs => 'Phương tiện, Liên kết và Tài liệu';

  @override
  String get starredMessages => 'Tin nhắn được đánh dấu';

  @override
  String get nickname => 'Biệt danh';

  @override
  String get none => 'Không';

  @override
  String get yes => 'Có';

  @override
  String get no => 'Không';

  @override
  String get exitGroup => 'Thoát khỏi nhóm';

  @override
  String get clickToAddGroupDescription => 'Nhấp để thêm mô tả nhóm';

  @override
  String get unBlockUser => 'Bỏ chặn người dùng';

  @override
  String get areYouSureToUnBlock => 'Bạn có chắc chắn muốn bỏ chặn';

  @override
  String get contactInfo => 'Thông tin liên hệ';

  @override
  String get audio => 'Âm thanh';

  @override
  String get video => 'Video';

  @override
  String get hiIamUse => 'Xin chào, tôi sử dụng';

  @override
  String get on => 'Bật';

  @override
  String get off => 'Tắt';

  @override
  String get unBlock => 'Bỏ chặn';

  @override
  String get block => 'Chặn';

  @override
  String get chooseAtLestOneMember => 'Chọn ít nhất một thành viên';

  @override
  String get close => 'Đóng';

  @override
  String get next => 'Tiếp theo';

  @override
  String get appMembers => 'Thành viên ứng dụng';

  @override
  String get create => 'Tạo';

  @override
  String get upgradeToAdmin => 'Nâng cấp thành quản trị viên';

  @override
  String get update => 'Cập nhật';

  @override
  String get deleteChat => 'Xóa cuộc trò chuyện';

  @override
  String get clearChat => 'Xóa trò chuyện';

  @override
  String get showHistory => 'Hiển thị lịch sử';

  @override
  String get groupIcon => 'Biểu tượng nhóm';

  @override
  String get tapToSelectAnIcon => 'Chạm để chọn một biểu tượng';

  @override
  String get groupDescription => 'Mô tả nhóm';

  @override
  String get more => 'Thêm';

  @override
  String get messageInfo => 'Thông tin tin nhắn';

  @override
  String get successfullyDownloadedIn => 'Tải xuống thành công trong';

  @override
  String get delivered => 'Đã gửi';

  @override
  String get read => 'Đã đọc';

  @override
  String get orLoginWith => 'Hoặc đăng nhập bằng';

  @override
  String get resetPassword => 'Đặt lại mật khẩu';

  @override
  String get otpCode => 'Mã OTP';

  @override
  String get newPassword => 'Mật khẩu mới';

  @override
  String get areYouSure => 'Bạn có chắc chắn?';

  @override
  String get broadcastMembers => 'Thành viên phát sóng';

  @override
  String get phone => 'Điện thoại';

  @override
  String get users => 'Người dùng';

  @override
  String get calls => 'Cuộc gọi';

  @override
  String get yourAreAboutToLogoutFromThisAccount =>
      'Bạn sắp đăng xuất khỏi tài khoản này';

  @override
  String get noUpdatesAvailableNow => 'Không có cập nhật nào có sẵn bây giờ';

  @override
  String get dataPrivacy => 'Quyền riêng tư dữ liệu';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      'Tất cả dữ liệu đã được sao lưu, bạn không cần quản lý và lưu trữ dữ liệu bằng chính mình! Nếu bạn đăng xuất và đăng nhập lại, bạn sẽ thấy tất cả cuộc trò chuyện giống như phiên bản web';

  @override
  String get account => 'Tài khoản';

  @override
  String get linkedDevices => 'Thiết bị đã liên kết';

  @override
  String get storageAndData => 'Lưu trữ và Dữ liệu';

  @override
  String get tellAFriend => 'Kể cho bạn';

  @override
  String get help => 'Trợ giúp';

  @override
  String get blockedUsers => 'Người dùng đã bị chặn';

  @override
  String get inAppAlerts => 'Thông báo trong ứng dụng';

  @override
  String get language => 'Ngôn ngữ';

  @override
  String get adminNotification => 'Thông báo quản trị';

  @override
  String get checkForUpdates => 'Kiểm tra cập nhật';

  @override
  String get linkByQrCode => 'Liên kết bằng mã QR';

  @override
  String get deviceStatus => 'Trạng thái thiết bị';

  @override
  String get desktopAndOtherDevices => 'Máy tính để bàn và các thiết bị khác';

  @override
  String get linkADeviceSoon => 'Liên kết thiết bị (Sắp tới)';

  @override
  String get lastActiveFrom => 'Hoạt động lần cuối từ';

  @override
  String get tapADeviceToEditOrLogOut =>
      'Chạm để chỉnh sửa hoặc đăng xuất khỏi thiết bị.';

  @override
  String get contactUs => 'Liên hệ chúng tôi';

  @override
  String get supportChatSoon => 'Trò chuyện hỗ trợ (Sắp tới)';

  @override
  String get updateYourName => 'Cập nhật tên của bạn';

  @override
  String get updateYourBio => 'Cập nhật tiểu sử của bạn';

  @override
  String get edit => 'Chỉnh sửa';

  @override
  String get about => 'Về chúng tôi';

  @override
  String get oldPassword => 'Mật khẩu cũ';

  @override
  String get deleteMyAccount => 'Xóa tài khoản của tôi';

  @override
  String get passwordHasBeenChanged => 'Mật khẩu đã được thay đổi';

  @override
  String get logoutFromAllDevices => 'Đăng xuất khỏi tất cả thiết bị?';

  @override
  String get updateYourPassword => 'Cập nhật mật khẩu của bạn';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      'Nhập tên của bạn và thêm một hình đại diện tùy chọn';

  @override
  String get privacyPolicy => 'Chính sách quyền riêng tư';

  @override
  String get chat => 'Trò chuyện';

  @override
  String get send => 'Gửi';

  @override
  String get reportHasBeenSubmitted => 'Báo cáo của bạn đã được gửi';

  @override
  String get offline => 'Ngoại tuyến';

  @override
  String get harassmentOrBullyingDescription =>
      'Quấy rối hoặc bắt nạt: Tùy chọn này cho phép người dùng báo cáo những người đang nhắm mục tiêu họ hoặc người khác bằng các tin nhắn quấy rối, đe dọa hoặc các hình thức khác của bắt nạt.';

  @override
  String get spamOrScamDescription =>
      'Spam hoặc Lừa đảo: Tùy chọn này dành cho người dùng báo cáo các tài khoản đang gửi tin nhắn rác, quảng cáo không mời hoặc đang cố gắng lừa đảo người khác.';

  @override
  String get areYouSureToReportUserToAdmin =>
      'Bạn có chắc chắn muốn báo cáo người dùng này cho quản trị viên?';

  @override
  String get groupWith => 'Nhóm với';

  @override
  String get inappropriateContentDescription =>
      'Nội dung không thích hợp: Người dùng có thể chọn tùy chọn này để báo cáo bất kỳ nội dung nào có tính chất tình dục, lời nói căm thù hoặc nội dung khác vi phạm các tiêu chuẩn của cộng đồng.';

  @override
  String get otherCategoryDescription =>
      'Khác: Danh mục tổng hợp này có thể được sử dụng cho vi phạm không dễ dàng rơi vào các danh mục trên. Có thể hữu ích nếu bao gồm một ô văn bản cho người dùng cung cấp thông tin bổ sung.';

  @override
  String get explainWhatHappens => 'Hãy giải thích điều gì xảy ra ở đây';

  @override
  String get loginAgain => 'Đăng nhập lại!';

  @override
  String get yourSessionIsEndedPleaseLoginAgain =>
      'Phiên của bạn đã kết thúc, vui lòng đăng nhập lại!';

  @override
  String get aboutToBlockUserWithConsequences =>
      'Bạn đang chuẩn bị chặn người dùng này. Bạn sẽ không thể gửi tin nhắn cho họ và không thể thêm họ vào các nhóm hoặc phát sóng!';

  @override
  String get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
      'Bạn đang sắp xóa tài khoản của bạn. Tài khoản của bạn sẽ không xuất hiện lại trong danh sách người dùng';

  @override
  String get admin => 'Quản trị viên';

  @override
  String get member => 'Thành viên';

  @override
  String get creator => 'Người tạo';

  @override
  String get currentDevice => 'Thiết bị hiện tại';

  @override
  String get visits => 'Lượt truy cập';

  @override
  String get chooseRoom => 'Chọn phòng';

  @override
  String get deleteThisDeviceDesc =>
      'Xóa thiết bị này có nghĩa là đăng xuất ngay lập tức khỏi thiết bị này';

  @override
  String get youAreAboutToUpgradeToAdmin =>
      'Bạn đang chuẩn bị thăng cấp thành quản trị viên';

  @override
  String get microphonePermissionMustBeAccepted =>
      'Microphone permission must be accepted';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'Microphone and camera permission must be accepted';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      'Hiện tại đăng nhập đã được phép. Vui lòng thử lại sau.';

  @override
  String get dashboard => 'Bảng điều khiển';

  @override
  String get notification => 'Thông báo';

  @override
  String get total => 'Tổng cộng';

  @override
  String get blocked => 'Bị chặn';

  @override
  String get deleted => 'Đã xóa';

  @override
  String get accepted => 'Đã chấp nhận';

  @override
  String get notAccepted => 'Không chấp nhận';

  @override
  String get web => 'Web';

  @override
  String get android => 'Android';

  @override
  String get macOs => 'macOS';

  @override
  String get windows => 'Windows';

  @override
  String get other => 'Khác';

  @override
  String get totalVisits => 'Tổng số lượt truy cập';

  @override
  String get totalMessages => 'Tổng số tin nhắn';

  @override
  String get textMessages => 'Tin nhắn văn bản';

  @override
  String get imageMessages => 'Tin nhắn hình ảnh';

  @override
  String get videoMessages => 'Tin nhắn video';

  @override
  String get voiceMessages => 'Tin nhắn thoại';

  @override
  String get fileMessages => 'Tin nhắn tệp';

  @override
  String get infoMessages => 'Tin nhắn thông tin';

  @override
  String get voiceCallMessages => 'Tin nhắn cuộc gọi thoại';

  @override
  String get videoCallMessages => 'Tin nhắn cuộc gọi video';

  @override
  String get locationMessages => 'Tin nhắn vị trí';

  @override
  String get directChat => 'Trò chuyện trực tiếp';

  @override
  String get group => 'Nhóm';

  @override
  String get broadcast => 'Truyền hình';

  @override
  String get messageCounter => 'Bộ đếm tin nhắn';

  @override
  String get roomCounter => 'Bộ đếm phòng';

  @override
  String get countries => 'Quốc gia';

  @override
  String get devices => 'Thiết bị';

  @override
  String get notificationTitle => 'Tiêu đề thông báo';

  @override
  String get notificationDescription => 'Mô tả thông báo';

  @override
  String get notificationsPage => 'Trang thông báo';

  @override
  String get updateFeedBackEmail => 'Cập nhật Email phản hồi';

  @override
  String get setMaxMessageForwardAndShare =>
      'Thiết lập số lượng tối đa tin nhắn chuyển tiếp và chia sẻ';

  @override
  String get setNewPrivacyPolicyUrl => 'Thiết lập URL chính sách bảo mật mới';

  @override
  String get forgetPasswordExpireTime => 'Thời gian hết hạn quên mật khẩu';

  @override
  String get callTimeoutInSeconds => 'Thời gian chờ cuộc gọi (giây)';

  @override
  String get setMaxGroupMembers => 'Thiết lập số lượng tối đa thành viên nhóm';

  @override
  String get setMaxBroadcastMembers =>
      'Thiết lập số lượng tối đa thành viên truyền hình';

  @override
  String get allowCalls => 'Cho phép cuộc gọi';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      'Nếu tùy chọn này được bật, cuộc gọi video và thoại sẽ được phép';

  @override
  String get allowAds => 'Cho phép quảng cáo';

  @override
  String get allowMobileLogin => 'Cho phép đăng nhập di động';

  @override
  String get allowWebLogin => 'Cho phép đăng nhập web';

  @override
  String get messages => 'Tin nhắn';

  @override
  String get appleStoreAppUrl => 'URL cửa hàng Apple App';

  @override
  String get googlePlayAppUrl => 'URL cửa hàng Google Play';

  @override
  String get privacyUrl => 'URL chính sách bảo mật';

  @override
  String get feedBackEmail => 'Email phản hồi';

  @override
  String get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
      'Nếu tùy chọn này bị vô hiệu hóa, việc gửi tệp tin, hình ảnh, video và vị trí trong trò chuyện sẽ bị chặn';

  @override
  String get allowSendMedia => 'Cho phép gửi phương tiện truyền thông';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      'Nếu tùy chọn này bị vô hiệu hóa, việc tạo phát sóng trong trò chuyện sẽ bị chặn';

  @override
  String get allowCreateBroadcast => 'Cho phép tạo phát sóng';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      'Nếu tùy chọn này bị vô hiệu hóa, việc tạo nhóm trò chuyện sẽ bị chặn';

  @override
  String get allowCreateGroups => 'Cho phép tạo nhóm';

  @override
  String get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
      'Nếu tùy chọn này bị vô hiệu hóa, việc đăng nhập hoặc đăng ký trên máy tính để bàn (Windows và macOS) sẽ bị chặn';

  @override
  String get allowDesktopLogin => 'Cho phép đăng nhập từ máy tính để bàn';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      'Nếu tùy chọn này bị vô hiệu hóa, việc đăng nhập hoặc đăng ký trực tuyến sẽ bị chặn';

  @override
  String get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
      'Nếu tùy chọn này được bật, quảng cáo Google sẽ xuất hiện trong trò chuyện';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'If this option is enabled, the Google Ads banner will appear in chats.';

  @override
  String get userProfile => 'Hồ sơ người dùng';

  @override
  String get userInfo => 'Thông tin người dùng';

  @override
  String get fullName => 'Họ và tên';

  @override
  String get bio => 'Giới thiệu bản thân';

  @override
  String get noBio => 'Không có giới thiệu bản thân';

  @override
  String get verifiedAt => 'Đã xác minh';

  @override
  String get country => 'Quốc gia';

  @override
  String get registerStatus => 'Trạng thái đăng ký';

  @override
  String get registerMethod => 'Phương thức đăng ký';

  @override
  String get banTo => 'Bị chặn đến ngày';

  @override
  String get deletedAt => 'Đã xóa vào ngày';

  @override
  String get createdAt => 'Đã tạo vào ngày';

  @override
  String get updatedAt => 'Đã cập nhật vào ngày';

  @override
  String get reports => 'Báo cáo';

  @override
  String get clickToSeeAllUserDevicesDetails =>
      'Nhấn để xem chi tiết tất cả thiết bị của người dùng';

  @override
  String get allDeletedMessages => 'Tất cả tin nhắn đã xóa';

  @override
  String get voiceCallMessage => 'Tin nhắn cuộc gọi thoại';

  @override
  String get totalRooms => 'Tổng số phòng';

  @override
  String get directRooms => 'Phòng trò chuyện trực tiếp';

  @override
  String get userAction => 'Hành động của người dùng';

  @override
  String get status => 'Trạng thái';

  @override
  String get joinedAt => 'Tham gia vào ngày';

  @override
  String get saveLogin => 'Lưu đăng nhập';

  @override
  String get passwordIsRequired => 'Yêu cầu mật khẩu';

  @override
  String get verified => 'Đã xác minh';

  @override
  String get pending => 'Đang chờ';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => 'Yêu cầu mô tả';

  @override
  String get seconds => 'giây';

  @override
  String get clickToSeeAllUserInformations =>
      'Nhấn để xem tất cả thông tin người dùng';

  @override
  String get clickToSeeAllUserCountries =>
      'Nhấn để xem tất cả quốc gia của người dùng';

  @override
  String get clickToSeeAllUserMessagesDetails =>
      'Nhấn để xem tất cả chi tiết tin nhắn của người dùng';

  @override
  String get clickToSeeAllUserRoomsDetails =>
      'Nhấn để xem tất cả chi tiết phòng của người dùng';

  @override
  String get clickToSeeAllUserReports =>
      'Nhấn để xem tất cả báo cáo của người dùng';

  @override
  String get banAt => 'Bị chặn vào ngày';

  @override
  String get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
      'Bây giờ bạn đăng nhập dưới dạng quản trị viên chỉ đọc. Tất cả chỉnh sửa bạn thực hiện sẽ không được áp dụng vì đây là phiên bản thử nghiệm.';

  @override
  String get createStory => 'Tạo câu chuyện';

  @override
  String get writeACaption => 'Viết chú thích...';

  @override
  String get storyCreatedSuccessfully => 'Câu chuyện đã được tạo thành công';

  @override
  String get stories => 'Câu chuyện';

  @override
  String get clear => 'Xóa';

  @override
  String get clearCallsConfirm => 'Xác nhận xóa cuộc gọi';

  @override
  String get chooseHowAutomaticDownloadWorks =>
      'Chọn cách hoạt động của tải xuống tự động';

  @override
  String get whenUsingMobileData => 'Khi sử dụng dữ liệu di động';

  @override
  String get whenUsingWifi => 'Khi sử dụng Wi-Fi';

  @override
  String get image => 'Hình ảnh';

  @override
  String get myPrivacy => 'Quyền riêng tư của tôi';

  @override
  String get createTextStory => 'Tạo câu chuyện văn bản';

  @override
  String get createMediaStory => 'Tạo câu chuyện đa phương tiện';

  @override
  String get camera => 'Máy ảnh';

  @override
  String get gallery => 'Thư viện ảnh';

  @override
  String get recentUpdate => 'Cập nhật gần đây';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => 'Thêm câu chuyện mới';

  @override
  String get updateYourProfile => 'Cập nhật hồ sơ của bạn';

  @override
  String get configureYourAccountPrivacy => 'Cấu hình quyền riêng tư tài khoản';

  @override
  String get youInPublicSearch => 'Bạn trong tìm kiếm công khai';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      'Hồ sơ của bạn xuất hiện trong tìm kiếm công khai và thêm vào nhóm';

  @override
  String get yourLastSeen => 'Lần cuối nhìn thấy';

  @override
  String get yourLastSeenInChats => 'Lần cuối nhìn thấy trong trò chuyện';

  @override
  String get startNewChatWithYou => 'Bắt đầu trò chuyện mới với bạn';

  @override
  String get yourStory => 'Câu chuyện của bạn';

  @override
  String get forRequest => 'Theo yêu cầu';

  @override
  String get public => 'Công khai';

  @override
  String get createYourStory => 'Tạo câu chuyện của bạn';

  @override
  String get shareYourStatus => 'Chia sẻ trạng thái của bạn';

  @override
  String get oneSeenMessage => 'Một tin nhắn đã xem';

  @override
  String get messageHasBeenViewed => 'Tin nhắn đã được xem';

  @override
  String get clickToSee => 'Nhấp để xem';

  @override
  String get images => 'Hình ảnh';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String get areYouSureRemoveAccount =>
      'Are you sure you want to remove this account?';

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';

  @override
  String get accountAddedSuccessfully => 'Account added successfully';

  @override
  String get addProfilePicture => 'Add Profile Picture';

  @override
  String get addProfilePictureSubtitle =>
      'Add a profile picture to help others recognize you';

  @override
  String get pleaseSelectProfilePicture => 'Please select a profile picture';

  @override
  String get uploading => 'Uploading...';

  @override
  String get continueText => 'Continue';

  @override
  String get profilePictureRequired =>
      'A profile picture is required to continue. Please select an image to upload.';

  @override
  String get shareProfile => 'Share Profile';

  @override
  String get liveStreams => 'Live Streams';

  @override
  String get goLive => 'Go Live';

  @override
  String get shareYourMomentsLive => 'Share your moments live with friends';

  @override
  String get viewers => 'viewers';

  @override
  String get join => 'Join';

  @override
  String get joinLiveStream => 'Join Live Stream';

  @override
  String get viewDetails => 'View Details';

  @override
  String get startLiveStream => 'Start Live Stream';

  @override
  String get endLiveStream => 'End Live Stream';

  @override
  String get leaveLiveStream => 'Leave Live Stream';

  @override
  String get inviteUsers => 'Invite Users';

  @override
  String get banUser => 'Ban User';

  @override
  String get unbanUser => 'Unban User';

  @override
  String get pinMessage => 'Pin Message';

  @override
  String get unpinMessage => 'Unpin Message';

  @override
  String get sendComment => 'Send Comment';

  @override
  String get addReaction => 'Add Reaction';

  @override
  String get liveStreamTitle => 'Live Stream Title';

  @override
  String get liveStreamDescription => 'Description (optional)';

  @override
  String get whoCanWatch => 'Who can watch?';

  @override
  String get everyone => 'Everyone';

  @override
  String get specificFriends => 'Specific Friends';

  @override
  String get selectFriends => 'Select Friends';

  @override
  String get applyFilters => 'Apply Filters';

  @override
  String get noFilters => 'No Filters';

  @override
  String get startStreaming => 'Start Streaming';

  @override
  String get streamEnded => 'Stream Ended';

  @override
  String get youAreNowLive => 'You are now live!';

  @override
  String get streamEndedByHost => 'Stream ended by host';

  @override
  String get removedFromStream => 'You have been removed from the stream';

  @override
  String get commentPlaceholder => 'Add a comment...';

  @override
  String get pinnedMessage => 'Pinned Message';

  @override
  String get hostControls => 'Host Controls';

  @override
  String get participants => 'Participants';

  @override
  String get comments => 'Comments';

  @override
  String get reactions => 'Reactions';
}
