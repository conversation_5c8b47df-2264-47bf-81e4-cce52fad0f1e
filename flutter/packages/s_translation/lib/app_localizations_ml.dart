// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Malayalam (`ml`).
class AppLocalizationsMl extends AppLocalizations {
  AppLocalizationsMl([String locale = 'ml']) : super(locale);

  @override
  String get done => 'അത്യുത്തമം';

  @override
  String get loading => 'ലോഡുചെയ്യുന്നു...';

  @override
  String get messageHasBeenDeleted => 'സന്ദേശം മായിച്ചിരിക്കുന്നു';

  @override
  String get mute => 'സമയം';

  @override
  String get cancel => 'റദ്ദാക്കുക';

  @override
  String get typing => 'ടൈപ്പിംഗ്...';

  @override
  String get ok => 'ശരി';

  @override
  String get recording => 'റെക്കോർഡുചെയ്യുന്നു...';

  @override
  String get connecting => 'കണക്റ്റിംഗ്...';

  @override
  String get deleteYouCopy => 'നിന്നെക്കുറിച്ച് നിന്നെക്കുറിച്ച് മായ്ക്കുക';

  @override
  String get unMute => 'അൺമ്യൂട്ട്';

  @override
  String get delete => 'മായ്ക്കുക';

  @override
  String get report => 'റിപ്പോർട്ട്';

  @override
  String get leaveGroup => 'ഗ്രൂപ്പ് അകലെ പോകുക';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      'നിനക്ക് നിന്നെക്കുറിച്ച് നിന്നെക്കുറിച്ച് അനുമതിപ്പെടുന്നതാണോ? ഈ പ്രവർത്തനം പുനഃസ്ഥാപിക്കാനാവില്ല';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      'നിനക്ക് ഈ ഗ്രൂപ്പ് അകലെ പോകാൻ തീർന്നുണ്ടോ? ഈ പ്രവർത്തനം പുനഃസ്ഥാപിക്കാനാവില്ല';

  @override
  String get leaveGroupAndDeleteYourMessageCopy =>
      'ഗ്രൂപ്പ് അകലെ പോകുകയും നിന്നയെക്കുറിച്ചുള്ള നിന്നയുടെ പരിശോധനയെ മായ്ക്കുക';

  @override
  String get vMessageInfoTrans => 'സന്ദേശ വിവരം';

  @override
  String get updateTitleTo => 'തലകൾ അപ്ഡേറ്റുചെയ്യുക';

  @override
  String get updateImage => 'ചിത്രം അപ്ഡേറ്റുചെയ്യുക';

  @override
  String get joinedBy => 'ചേർന്നത്';

  @override
  String get promotedToAdminBy => 'അഡ്മിൻ ആക്കിയത്';

  @override
  String get dismissedToMemberBy => 'അംഗത്വത്തിന്റെ പുറത്തിറക്കി';

  @override
  String get leftTheGroup => 'ഗ്രൂപ്പ് അകലെ പോയി';

  @override
  String get you => 'നിന്നെ';

  @override
  String get kickedBy => 'അപകടപ്പെട്ടത്';

  @override
  String get groupCreatedBy => 'ഗ്രൂപ്പ് സൃഷ്ടിച്ചത്';

  @override
  String get groupDeletedBy => 'Group deleted by';

  @override
  String get addedYouToNewBroadcast =>
      'നിന്നെ പുതിയ ബ്രോഡ്കാസ്റ്റിലേക്ക് ചേർത്തു';

  @override
  String get download => 'ഡൗൺലോഡുചെയ്യുക';

  @override
  String get copy => 'പകർത്തുക';

  @override
  String get info => 'വിവരം';

  @override
  String get share => 'പങ്കിടുക';

  @override
  String get forward => 'മുന്നിൽ അയക്കുക';

  @override
  String get reply => 'പ്രതികരിക്കുക';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => 'എല്ലായിടത്തും മായ്ക്കുക';

  @override
  String get deleteFromMe => 'എനിന്നെക്കുറിച്ച് മായ്ക്കുക';

  @override
  String get downloading => 'ഡൗൺലോഡുചെയ്യുന്നു...';

  @override
  String get fileHasBeenSavedTo => 'ഫയൽ സേവ് ചെയ്തിരിക്കുന്നു';

  @override
  String get online => 'ഓൺ‌ലൈൻ';

  @override
  String get youDontHaveAccess => 'നിനക്ക് അക്കസ്സ് ഇല്ല';

  @override
  String get replyToYourSelf => 'നിനക്ക് സ്വന്തമായി പ്രതികരിക്കുക';

  @override
  String get repliedToYourSelf =>
      'നിന്നെക്കുറിച്ച് നിന്നെക്കുറിച്ച് പ്രതികരിച്ചു';

  @override
  String get audioCall => 'ഓഡിയോ കോൾ';

  @override
  String get ring => 'റിംഗ്';

  @override
  String get canceled => 'റദ്ദാക്കി';

  @override
  String get timeout => 'സമയപ്രാപിക്കൽ';

  @override
  String get rejected => 'നിരാകരിച്ചു';

  @override
  String get finished => 'പൂർത്തിയായി';

  @override
  String get inCall => 'കോൾ സമയത്ത്';

  @override
  String get sessionEnd => 'സെഷൻ അവസാനിച്ചു';

  @override
  String get yesterday => 'ഇന്നലെ';

  @override
  String get today => 'ഇന്ന്';

  @override
  String get textFieldHint => 'ഒരു സന്ദേശം ടൈപ്പ് ചെയ്യുക...';

  @override
  String get files => 'ഫയലുകൾ';

  @override
  String get location => 'സ്ഥലം';

  @override
  String get shareMediaAndLocation => 'മീഡിയ ഒപ്പം സ്ഥലം പങ്കിടുക';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize =>
      'അനുമതിച്ച പരിമിത വലിപ്പത്തിൽ കരിയാത്ത വീഡിയോ ഉണ്ടായിരിക്കുന്നു';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize =>
      'അനുമതിച്ച പരിമിത വലിപ്പത്തിൽ കരിയാത്ത ഫയൽ ഉണ്ടായിരിക്കുന്നു';

  @override
  String get makeCall => 'കോൾ ചെയ്യുക';

  @override
  String get areYouWantToMakeVideoCall => 'നിനക്ക് വീഡിയോ കോൾ ചെയ്യണമോ?';

  @override
  String get areYouWantToMakeVoiceCall => 'നിനക്ക് ശബ്ദം കോൾ ചെയ്യണമോ?';

  @override
  String get vMessagesInfoTrans => 'സന്ദേശ വിവരം';

  @override
  String get star => 'നടന്നു';

  @override
  String get minutes => 'മിനിറ്റുകൾ';

  @override
  String get sendMessage => 'സന്ദേശം അയയ്ക്കുക';

  @override
  String get deleteUser => 'ഉപയോക്താവ് മായ്ക്കുക';

  @override
  String get actions => 'പ്രവർത്തനങ്ങൾ';

  @override
  String get youAreAboutToDeleteThisUserFromYourList =>
      'നിനക്ക് ഈ ഉപയോക്താവിനെ നിന്നെക്കുറിച്ച് മായ്ക്കാൻ തീർന്നുണ്ടോ?';

  @override
  String get updateBroadcastTitle => 'ബ്രോഡ്കാസ്റ്റ് തലകൾ അപ്ഡേറ്റുചെയ്യുക';

  @override
  String get usersAddedSuccessfully => 'ഉപയോക്താക്കൾ വിജയകരമായി ചേർക്കപ്പെട്ടു';

  @override
  String get broadcastSettings => 'ബ്രോഡ്കാസ്റ്റ് ക്രമീകരണങ്ങൾ';

  @override
  String get addParticipants => 'ഭാഗസ്വാമികളെ ചേർക്കുക';

  @override
  String get broadcastParticipants => 'ബ്രോഡ്കാസ്റ്റ് പങ്കാളികൾ';

  @override
  String get updateGroupDescription => 'ഗ്രൂപ്പ് വിവരം അപ്ഡേറ്റുചെയ്യുക';

  @override
  String get updateGroupTitle => 'ഗ്രൂപ്പ് തലകൾ അപ്ഡേറ്റുചെയ്യുക';

  @override
  String get groupSettings => 'ഗ്രൂപ്പ് ക്രമീകരണങ്ങൾ';

  @override
  String get description => 'വിവരണം';

  @override
  String get muteNotifications => 'അലക്ക് അറിയിപ്പുകൾ';

  @override
  String get groupParticipants => 'ഗ്രൂപ്പ് പങ്കാളികൾ';

  @override
  String get blockUser => 'ഉപയോക്താവ് അടക്കുക';

  @override
  String get areYouSureToBlock => 'നിനക്ക് അടക്കണമോ?';

  @override
  String get userPage => 'ഉപയോക്തൃ പേജ്';

  @override
  String get starMessage => 'സ്റ്റാർ സന്ദേശം';

  @override
  String get showMedia => 'മീഡിയ കാണിക്കുക';

  @override
  String get reportUser => 'ഉപയോക്താവ് റിപ്പോർട്ട് ചെയ്യുക';

  @override
  String get groupName => 'ഗ്രൂപ്പ് പേര്';

  @override
  String get changeSubject => 'വിഷയം മാറ്റുക';

  @override
  String get titleIsRequired => 'തലകൾ ആവശ്യമാണ്';

  @override
  String get createBroadcast => 'ബ്രോഡ്കാസ്റ്റ് സൃഷ്ടിക്കുക';

  @override
  String get broadcastName => 'ബ്രോഡ്കാസ്റ്റ് പേര്';

  @override
  String get createGroup => 'ഗ്രൂപ്പ് സൃഷ്ടിക്കുക';

  @override
  String get forgetPassword => 'പാസ്വേഡ് മറന്നുകളയുക';

  @override
  String get globalSearch => 'ലോകാണോട്ടം';

  @override
  String get dismissesToMember => 'അംഗത്വത്തിലേക്ക് അപകടപ്പെട്ടു';

  @override
  String get setToAdmin => 'അഡ്മിൻ ആക്കുക';

  @override
  String get kickMember => 'അംഗം പുറത്താക്കുക';

  @override
  String get youAreAboutToDismissesToMember =>
      'നിനക്ക് അംഗത്വത്തിലേക്ക് അപകടപ്പെട്ടു';

  @override
  String get youAreAboutToKick => 'നിനക്ക് അംഗം പുറത്താക്കാൻ തീർന്നുണ്ടോ?';

  @override
  String get groupMembers => 'ഗ്രൂപ്പ് അംഗങ്ങൾ';

  @override
  String get tapForPhoto => 'ഫോട്ടോക്കാണുകയായി ടാപ്പുചെയ്യുക';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      'We high recommend to download this update';

  @override
  String get newGroup => 'പുതിയ ഗ്രൂപ്പ്';

  @override
  String get newBroadcast => 'പുതിയ ബ്രോഡ്കാസ്റ്റ്';

  @override
  String get starredMessage => 'സ്റ്റാർ ചെയ്ത സന്ദേശം';

  @override
  String get settings => 'സെറ്റിങ്ങുകൾ';

  @override
  String get chats => 'ചാറ്റുകൾ';

  @override
  String get recentUpdates => 'പുതിയ പുനരാപടൽ';

  @override
  String get startChat => 'ചാറ്റ് ആരംഭിക്കുക';

  @override
  String get newUpdateIsAvailable => 'പുതിയ അപ്ഡേറ്റ് ലഭ്യമാണ്';

  @override
  String get emailNotValid => 'ഇമെയിൽ സാധുവായില്ല';

  @override
  String get passwordMustHaveValue => 'പാസ്വേഡ് മൂല്യവയ്ക്കേണ്ടത്';

  @override
  String get error => 'പിശക്';

  @override
  String get password => 'പാസ്വേഡ്';

  @override
  String get login => 'ലോഗിൻ';

  @override
  String get needNewAccount => 'പുതിയ അക്കൗണ്ട് ആവശ്യമുണ്ട്?';

  @override
  String get register => 'രജിസ്റ്റർ';

  @override
  String get nameMustHaveValue => 'പേര് മൂല്യവയ്ക്കേണ്ടത്';

  @override
  String get passwordNotMatch => 'പാസ്വേഡ് സരിയായി പരിശോധിക്കുന്നില്ല';

  @override
  String get name => 'പേര്';

  @override
  String get email => 'ഇമെയിൽ';

  @override
  String get confirmPassword => 'പാസ്വേഡ് സ്ഥിരീകരിക്കുക';

  @override
  String get alreadyHaveAnAccount =>
      'ഇതാണ് നിനക്ക് ഇപ്പോൾ ഒരു അക്കൗണ്ട് ഉണ്ടായിരിക്കുന്നത്?';

  @override
  String get logOut => 'ലോഗൗട്ട്';

  @override
  String get back => 'തിരികെ';

  @override
  String get sendCodeToMyEmail => 'എന്റെ ഇമെയിലിലേക്ക് കോഡ് അയയ്ക്കുക';

  @override
  String get invalidLoginData => 'അസാധുവായ ലോഗിൻ ഡാറ്റ';

  @override
  String get userEmailNotFound => 'ഉപയോക്താവിന്റെ ഇമെയിൽ കണ്ടെത്തിയില്ല';

  @override
  String get yourAccountBlocked => 'നിന്റെ അക്കൗണ്ട് പ്രതിഷേധിച്ചു';

  @override
  String get yourAccountDeleted => 'നിന്റെ അക്കൗണ്ട് ഇല്ലാതാക്കി';

  @override
  String get userAlreadyRegister =>
      'ഉപയോക്താവ് ഇതിനകം രജിസ്റ്റർ ചെയ്തിരിക്കുന്നു';

  @override
  String get codeHasBeenExpired => 'കോഡ് കാലഹരണ്യത്തിനുള്ളിൽ';

  @override
  String get invalidCode => 'അസാധുവായ കോഡ്';

  @override
  String get whileAuthCanFindYou =>
      'അതിനാൽ ആഡ്ധനിക്കൽ നിന്നെ കണ്ടെത്താൻ കഴിയില്ല';

  @override
  String get userRegisterStatusNotAcceptedYet =>
      'ഉപയോക്താവ് രജിസ്റ്റർ ചെയ്തിട്ടില്ല';

  @override
  String get deviceHasBeenLogoutFromAllDevices =>
      'ഉപകരണം എല്ലാ ഉപകരണങ്ങളിലും ലോഗൗട്ട് ചെയ്തു';

  @override
  String get userDeviceSessionEndDeviceDeleted =>
      'ഉപയോക്താവിന്റെ ഉപകരണം സെഷൻ അവസാനിച്ചു, ഉപകരണം ഇല്ലാതാക്കി';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      'ഇമെയിൽ സ്ഥിരീകരിക്കാൻ നിനക്ക് കോഡ് അയയ്ക്കാൻ പോലുമില്ല';

  @override
  String get roomAlreadyInCall => 'റൂം ഇതാണ് വിളക്കുന്നത്';

  @override
  String get peerUserInCallNow => 'ഉപയോക്താവ് ഇപ്പോൾ കോൾ ചെയ്യുന്നു';

  @override
  String get callNotAllowed => 'കോൾ അനുവദനീയമല്ല';

  @override
  String get peerUserDeviceOffline => 'പീർ ഉപയോക്താവിന്റെ ഉപകരണം ഓഫ്ലൈൻ ആണ്';

  @override
  String get emailMustBeValid => 'Email must be valid';

  @override
  String get wait2MinutesToSendMail => 'Wait 2 minutes to send mail';

  @override
  String get codeMustEqualToSixNumbers => 'Code must equal to six numbers';

  @override
  String get newPasswordMustHaveValue => 'New password must have value';

  @override
  String get confirmPasswordMustHaveValue => 'Confirm password must have value';

  @override
  String get congregationsYourAccountHasBeenAccepted =>
      'Congregations your account has been accepted';

  @override
  String get yourAccountIsUnderReview => 'Your account is under review';

  @override
  String get waitingList => 'Waiting List';

  @override
  String get welcome => 'Welcome';

  @override
  String get retry => 'Retry';

  @override
  String get deleteMember => 'Delete member';

  @override
  String get profile => 'Profile';

  @override
  String get broadcastInfo => 'Broadcast info';

  @override
  String get updateTitle => 'Update title';

  @override
  String get members => 'Members';

  @override
  String get addMembers => 'Add Members';

  @override
  String get success => 'Success';

  @override
  String get media => 'Media';

  @override
  String get docs => 'Docs';

  @override
  String get links => 'Links';

  @override
  String get soon => 'Soon';

  @override
  String get unStar => 'Un star';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      'Update group description will update all group members';

  @override
  String get updateNickname => 'Update nickname';

  @override
  String get groupInfo => 'Group info';

  @override
  String get youNotParticipantInThisGroup =>
      'You not participant in this group';

  @override
  String get search => 'Search';

  @override
  String get mediaLinksAndDocs => 'Media, Links, and Docs';

  @override
  String get starredMessages => 'Starred Messages';

  @override
  String get nickname => 'Nickname';

  @override
  String get none => 'None';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get exitGroup => 'Exit Group';

  @override
  String get clickToAddGroupDescription => 'Click to add group description';

  @override
  String get unBlockUser => 'Un block user';

  @override
  String get areYouSureToUnBlock => 'Are you sure to un block';

  @override
  String get contactInfo => 'Contact info';

  @override
  String get audio => 'Audio';

  @override
  String get video => 'Video';

  @override
  String get hiIamUse => 'Hi iam using';

  @override
  String get on => 'On';

  @override
  String get off => 'Off';

  @override
  String get unBlock => 'Un Block';

  @override
  String get block => 'Block';

  @override
  String get chooseAtLestOneMember => 'Choose at lest one member';

  @override
  String get close => 'Close';

  @override
  String get next => 'Next';

  @override
  String get appMembers => 'App members';

  @override
  String get create => 'Create';

  @override
  String get upgradeToAdmin => 'Upgrade to admin';

  @override
  String get update => 'Update';

  @override
  String get deleteChat => 'Delete chat';

  @override
  String get clearChat => 'Clear chat';

  @override
  String get showHistory => 'Show history';

  @override
  String get groupIcon => 'ഗ്രൂപ്പ് ഐക്കൺ';

  @override
  String get tapToSelectAnIcon => 'ഐക്കൺ തിരഞ്ഞെടുക്കുന്നതിന് ടാപ്പുചെയ്യുക';

  @override
  String get groupDescription => 'ഗ്രൂപ്പ് വിവരണം';

  @override
  String get more => 'കൂടുതൽ';

  @override
  String get messageInfo => 'സന്ദേശ വിവരം';

  @override
  String get successfullyDownloadedIn => 'വിജയകരമായി ഡൌൺലോഡുചെയ്തത്';

  @override
  String get delivered => 'വഹിക്കപ്പെട്ടു';

  @override
  String get read => 'വായിച്ചു';

  @override
  String get orLoginWith => 'അല്ലെങ്കിൽ ലോഗിൻ ചെയ്യുക';

  @override
  String get resetPassword => 'പാസ്വേഡ് പുനഃസ്ഥാപിക്കുക';

  @override
  String get otpCode => 'OTP കോഡ്';

  @override
  String get newPassword => 'പുതിയ പാസ്വേഡ്';

  @override
  String get areYouSure => 'നിനക്ക് ഖചിതമാണോ?';

  @override
  String get broadcastMembers => 'ബ്രോഡ്കാസ്റ്റ് അംഗങ്ങൾ';

  @override
  String get phone => 'ഫോൺ';

  @override
  String get users => 'ഉപയോക്താക്കൾ';

  @override
  String get calls => 'കള്ളകൾ';

  @override
  String get yourAreAboutToLogoutFromThisAccount =>
      'നിനക്ക് ഈ അക്കൗണ്ടിൽ നിന്നും ലോഗൗട്ട് ചെയ്യുന്നതാണ്';

  @override
  String get noUpdatesAvailableNow => 'ഇപ്പോൾ പുതിയതുകൾ ലഭ്യമില്ല';

  @override
  String get dataPrivacy => 'ഡാറ്റ സ്വകാര്യത';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      'എല്ലാ ഡാറ്റയും ബാക്കപ്പ് ചെയ്തു, നിനക്ക് ഡാറ്റ സ്വന്തമാക്കാൻ അനുവദിക്കുന്നില്ല! ലോഗൗട്ട് ചെയ്യുന്നതിനുശേഷം നിനക്ക് എല്ലാ ചാറ്റുകൾക്കും ഒരുപോലെ കാണാൻ കാണിക്കും';

  @override
  String get account => 'അക്കൗണ്ട്';

  @override
  String get linkedDevices => 'ലിങ്ക് ചെയ്ത ഉപകരണങ്ങൾ';

  @override
  String get storageAndData => 'സ്റ്റോറേജ് ഒരുക്കലും ഡാറ്റ';

  @override
  String get tellAFriend => 'ഒരു സുഹൃത്തിനെ പറയുക';

  @override
  String get help => 'സഹായം';

  @override
  String get blockedUsers => 'ബ്ലോക്കുചെയ്ത ഉപയോക്താക്കൾ';

  @override
  String get inAppAlerts => 'ഇൻ-ആപ്പ് അലേർട്ടുകൾ';

  @override
  String get language => 'ഭാഷ';

  @override
  String get adminNotification => 'അഡ്മിൻ അറിയിപ്പ്';

  @override
  String get checkForUpdates => 'അപ്ഡേറ്റുകൾ പരിശോധിക്കുക';

  @override
  String get linkByQrCode => 'QR കോഡിനെ അടയ്ക്കുക';

  @override
  String get deviceStatus => 'ഉപകരണ സ്ഥിതി';

  @override
  String get desktopAndOtherDevices => 'ഡെസ്ക്ടോപ്പ്, മറ്റ് ഉപകരണങ്ങൾ';

  @override
  String get linkADeviceSoon => 'ഉപകരണം ലിങ്ക് ചെയ്യുക (ശീഘ്രം)';

  @override
  String get lastActiveFrom => 'അവസാന സക്രിയമായത്';

  @override
  String get tapADeviceToEditOrLogOut =>
      'എഡിറ്റ് അല്ലെങ്കിൽ ലോഗൗട്ട് ചെയ്യുന്നതിനായി ഒരു ഉപകരണം ടാപ്പുചെയ്യുക.';

  @override
  String get contactUs => 'ഞങ്ങളെ സമീപിക്കുക';

  @override
  String get supportChatSoon => 'സപ്പോർട്ട് ചാറ്റ് (ശീഘ്രം)';

  @override
  String get updateYourName => 'നിന്നിന്നും പേര് പരിഷ്കരിക്കുക';

  @override
  String get updateYourBio => 'നിനക്ക് സ്വന്തമായ ജീവചരിത്രം പരിഷ്കരിക്കുക';

  @override
  String get edit => 'എഡിറ്റുചെയ്യുക';

  @override
  String get about => 'പരിപാടി';

  @override
  String get oldPassword => 'പഴയ പാസ്വേഡ്';

  @override
  String get deleteMyAccount => 'എന്നെ ഇല്ലാതാക്കുക';

  @override
  String get passwordHasBeenChanged => 'പാസ്വേഡ് മാറ്റിച്ചു';

  @override
  String get logoutFromAllDevices => 'എല്ലാ ഉപകരണങ്ങളിലും ലോഗൗട്ട് ചെയ്യുക?';

  @override
  String get updateYourPassword => 'നിനക്ക് പാസ്വേഡ് പരിഷ്കരിക്കുക';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      'നിന്നെയുടെ പേര് നൽകുക അല്ലെങ്കിൽ ഒരു ഓപ്ഷണൽ പ്രൊഫൈൽ ചിത്രം ചേർക്കുക';

  @override
  String get privacyPolicy => 'സ്വകാര്യത നയം';

  @override
  String get chat => 'ചാറ്റ്';

  @override
  String get send => 'അയയ്ക്കുക';

  @override
  String get reportHasBeenSubmitted => 'നിനക്ക് അയയ്ക്കൽ സമർപ്പിച്ചു';

  @override
  String get offline => 'ഓഫ്ലൈൻ';

  @override
  String get harassmentOrBullyingDescription =>
      'പരിവർത്തനം അല്ലെങ്കിൽ ബുളിയിങ്ങ്: ഉപയോക്താക്കൾക്ക് താൽപര്യപ്പെടുന്ന സന്ദേശങ്ങളോ, ആക്രമണ സന്ദേശങ്ങളോ, ബുളിയിംഗിനോടുള്ള ആക്രമണങ്ങളോ വേണ്ടി അറിയിക്കാൻ അനുവദിക്കുന്നു.';

  @override
  String get spamOrScamDescription =>
      'സ്പാം അല്ലെങ്കിൽ സ്കാം: ഉപയോക്താക്കൾ സ്പാം സന്ദേശങ്ങൾ, അനേക പ്രചാരണങ്ങൾ, അല്ലും മറ്റ് ആളുകൾക്ക് പ്രയത്നിക്കുന്ന കമ്പ്യൂട്ടർ ദ്വാരം മുന്നറിയിപ്പ് ചെയ്യുന്ന അക്കൗണ്ടുകൾ അറിയിക്കാൻ അനുവദിക്കുന്നു.';

  @override
  String get areYouSureToReportUserToAdmin =>
      'നിനക്ക് ഈ ഉപയോക്താവിനെ അഡ്മിനുകൾക്ക് റിപ്പോർട്ട് സമർപ്പിക്കാൻ ഖചിതമാണോ?';

  @override
  String get groupWith => 'സംഗമം വേണ്ടി';

  @override
  String get inappropriateContentDescription =>
      'അനുചിത ഉള്ളടക്കം: ഉപയോക്താക്കൾ ഇതിനകം കമ്മ്യൂണിറ്റി മാനദണ്ഡങ്ങൾ ഉല്ലംഘിക്കുന്ന സെക്സ്വായിന്യാട്ടി ഉള്ളടക്കമായ ഉള്ളടക്കമോ, ദ്വേഷ പ്രസംഗങ്ങൾ, അല്ലും മറ്റ് കണ്ടെത്താൻ അനുവദിക്കുന്നു.';

  @override
  String get otherCategoryDescription =>
      'മറ്റുള്ള: ഉപയോക്താക്കൾക്ക് ഉപയോഗിക്കാവാത്ത ഉപയോക്താക്കൾക്കും അനുവദിക്കാവാത്ത ഉപയോക്താക്കൾക്കും പശ്ചാത്തല വിവരങ്ങള് എഴുതിക്കൊടുക്കാൻ ഇത് ഉപയോഗിക്കാവുന്നു.';

  @override
  String get explainWhatHappens =>
      'എന്താണ് സംഭവിക്കുന്നത് എന്ന് വിശദമായി വിവരിക്കുക';

  @override
  String get loginAgain => 'വീണ്ടും ലോഗിൻ ചെയ്യുക!';

  @override
  String get yourSessionIsEndedPleaseLoginAgain =>
      'നിനക്ക് സെഷൻ അവസാനിച്ചു, ദയവായി വീണ്ടും ലോഗിൻ ചെയ്യുക!';

  @override
  String get aboutToBlockUserWithConsequences =>
      'നിനക്ക് ഈ ഉപയോക്താവിനെ ബ്ലോക്ക് ചെയ്യാൻ ആഗ്രഹിക്കുന്നു. നിനക്ക് അവൻറെയോട് ചാറ്റുകൾ അയയ്ക്കാൻ അനുവദിക്കാൻ അല്ലെങ്കിൽ അവനെ ഗ്രൂപ്പുകളിൽ അല്ലെങ്കിൽ ബ്രോഡ്കാസ്റ്റുകളിൽ ചേർക്കാൻ കഴിയില്ല!';

  @override
  String get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
      'നിനക്ക് ഈ അക്കൗണ്ട് ഇല്ലാതാക്കാൻ ആഗ്രഹിക്കുന്നു. നിന്നെ പ്രയോഗിക്കുന്ന ഉപയോക്താക്കൾ പട്ടികയിൽ വീണ്ടും കാണാൻ കഴിയില്ല';

  @override
  String get admin => 'അഡ്മിൻ';

  @override
  String get member => 'അംഗം';

  @override
  String get creator => 'സൃഷ്ടാവ്';

  @override
  String get currentDevice => 'നിലവിലെ ഉപകരണം';

  @override
  String get visits => 'സന്ദർശനങ്ങൾ';

  @override
  String get chooseRoom => 'ഒരു കുറിപ്പ് തിരഞ്ഞെടുക്കുക';

  @override
  String get deleteThisDeviceDesc => 'ഈ ഉപകരണം ഇടപഴക്കാൻ പുറത്താക്കുന്നു';

  @override
  String get youAreAboutToUpgradeToAdmin => 'നിനക്ക് അഡ്മിൻ ആക്കാൻ തീർന്നുണ്ടോ';

  @override
  String get microphonePermissionMustBeAccepted =>
      'Microphone permission must be accepted';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'Microphone and camera permission must be accepted';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      'ഇപ്പോൾ ലോഗിൻ അനുവദനീകരിക്കുന്നതല്ല. ദയവായി ശേഷം ശ്രമിക്കുക.';

  @override
  String get dashboard => 'ഡാഷ്ബോർഡ്';

  @override
  String get notification => 'അറിയിപ്പ്';

  @override
  String get total => 'ആകെ';

  @override
  String get blocked => 'നിരോധിച്ചു';

  @override
  String get deleted => 'ഇല്ലാതാക്കി';

  @override
  String get accepted => 'അംഗീകരിച്ചു';

  @override
  String get notAccepted => 'അംഗീകരിച്ചില്ല';

  @override
  String get web => 'വെബ്';

  @override
  String get android => 'ആൻഡ്രോയ്ഡ്';

  @override
  String get macOs => 'മാക്‌ഒഎസ്';

  @override
  String get windows => 'വിൻഡോസ്';

  @override
  String get other => 'മറ്റുള്ളവ';

  @override
  String get totalVisits => 'ആകെ സന്ദർശനങ്ങൾ';

  @override
  String get totalMessages => 'ആകെ സന്ദേശങ്ങൾ';

  @override
  String get textMessages => 'ടെക്സ്റ്റ് സന്ദേശങ്ങൾ';

  @override
  String get imageMessages => 'ചിത്രം സന്ദേശങ്ങൾ';

  @override
  String get videoMessages => 'വീഡിയോ സന്ദേശങ്ങൾ';

  @override
  String get voiceMessages => 'വോയ്സ് സന്ദേശങ്ങൾ';

  @override
  String get fileMessages => 'ഫയൽ സന്ദേശങ്ങൾ';

  @override
  String get infoMessages => 'ഇൻഫോ സന്ദേശങ്ങൾ';

  @override
  String get voiceCallMessages => 'വോയ്സ് കോൾ സന്ദേശങ്ങൾ';

  @override
  String get videoCallMessages => 'വീഡിയോ കോൾ സന്ദേശങ്ങൾ';

  @override
  String get locationMessages => 'സ്ഥലം സന്ദേശങ്ങൾ';

  @override
  String get directChat => 'സ്വന്തമായ ചാറ്റ്';

  @override
  String get group => 'ഗ്രൂപ്പ്';

  @override
  String get broadcast => 'പ്രസാരണം';

  @override
  String get messageCounter => 'സന്ദേശം എണ്ണം';

  @override
  String get roomCounter => 'റൂം എണ്ണം';

  @override
  String get countries => 'രാജ്യങ്ങൾ';

  @override
  String get devices => 'ഉപകരണങ്ങൾ';

  @override
  String get notificationTitle => 'അറിയിപ്പ് ശീർഷകം';

  @override
  String get notificationDescription => 'അറിയിപ്പ് വിശദവബണ്ടം';

  @override
  String get notificationsPage => 'അറിയിപ്പ് പേജ്';

  @override
  String get updateFeedBackEmail => 'പ്രതിപാദന ഇമെയിൽ അപ്ഡേറ്റ് ചെയ്യുക';

  @override
  String get setMaxMessageForwardAndShare => 'പ്രതിപാദന അപ്ഡേറ്റ് ചെയ്യുക';

  @override
  String get setNewPrivacyPolicyUrl => 'പുതിയ പ്രൈവസി പോളിസി URL സജീവമാക്കുക';

  @override
  String get forgetPasswordExpireTime =>
      'പാസ്വേഡ് എങ്ങനെ മറന്നാണ് എന്നറിയപ്പെടുന്ന സമയം';

  @override
  String get callTimeoutInSeconds => 'വിളി സമയം (സെക്കന്റുകൾക്ക്)';

  @override
  String get setMaxGroupMembers =>
      'അധികതമ ഗ്രൂപ്പ് അംഗങ്ങളുടെ എണ്ണം സജീവമാക്കുക';

  @override
  String get setMaxBroadcastMembers =>
      'അധികതമ ബ്രോഡ്കാസ്റ്റ് അംഗങ്ങളുടെ എണ്ണം സജീവമാക്കുക';

  @override
  String get allowCalls => 'കോൾ അനുവദിക്കുക';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      'ഈ ഓപ്ഷൻ സജീവമാക്കിയാൽ വീഡിയോ ഒഴിവും വോയ്സ് കോൾ അനുവദിക്കുന്നു';

  @override
  String get allowAds => 'വാങ്ങിക്കുക അനുവദിക്കുക';

  @override
  String get allowMobileLogin => 'മൊബൈൽ ലോഗിൻ അനുവദിക്കുക';

  @override
  String get allowWebLogin => 'വെബ് ലോഗിൻ അനുവദിക്കുക';

  @override
  String get messages => 'സന്ദേശങ്ങൾ';

  @override
  String get appleStoreAppUrl => 'ആപ്പിൾ സ്റ്റോർ ആപ്ലിക്കേഷൻ URL';

  @override
  String get googlePlayAppUrl => 'ഗൂഗിൾ പ്ലേ ആപ്ലിക്കേഷൻ URL';

  @override
  String get privacyUrl => 'സ്വകാര്യത URL';

  @override
  String get feedBackEmail => 'പ്രതിപാദന ഇമെയിൽ';

  @override
  String get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
      'ഈ ഓപ്ഷൻ അമാന്യമാക്കിയാൽ ചാറ്റ് ഫയലുകൾ, ചിത്രങ്ങൾ, വീഡിയോകൾ, സ്ഥാനം അയച്ചേക്കാനും';

  @override
  String get allowSendMedia => 'മീഡിയ അയച്ചേക്കാനും അനുവദിക്കുക';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      'ഈ ഓപ്ഷൻ അമാന്യമാക്കിയാൽ ചാറ്റ് പ്രസാരണം സൃഷ്ടിക്കപ്പെടും';

  @override
  String get allowCreateBroadcast => 'പ്രസാരണം സൃഷ്ടിക്കാൻ അനുവദിക്കുക';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      'ഈ ഓപ്ഷൻ അമാന്യമാക്കിയാൽ ചാറ്റ് ഗ്രൂപ്പുകൾ സൃഷ്ടിക്കപ്പെടും';

  @override
  String get allowCreateGroups => 'ഗ്രൂപ്പുകൾ സൃഷ്ടിക്കാൻ അനുവദിക്കുക';

  @override
  String get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
      'ഈ ഓപ്ഷൻ അമാന്യമാക്കിയാൽ ഡെസ്ക്ടോപ്പ് ലോഗിൻ അല്ലെങ്കിൽ അമർത്തുക (വിൻഡോസ് മറ്റും macOS) നിരോധിച്ചുകൊള്ളും';

  @override
  String get allowDesktopLogin => 'ഡെസ്ക്ടോപ്പ് ലോഗിൻ അനുവദിക്കുക';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      'ഈ ഓപ്ഷൻ അമാന്യമാക്കിയാൽ വെബ് ലോഗിൻ അല്ലെങ്കിൽ അമർത്തുക';

  @override
  String
      get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
          'ഈ ഓപ്ഷൻ സജീവമാക്കിയാൽ Google അഡ്സ് ബാനർ ചാറ്റുകളിൽ കാണാനും';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'If this option is enabled, the Google Ads banner will appear in chats.';

  @override
  String get userProfile => 'ഉപയോക്തൃ പ്രൊഫൈൽ';

  @override
  String get userInfo => 'ഉപയോക്തൃ വിവരങ്ങൾ';

  @override
  String get fullName => 'പൂർണ്ണ പേര്';

  @override
  String get bio => 'ബയോ';

  @override
  String get noBio => 'ബയോ ഇല്ല';

  @override
  String get verifiedAt => 'പ്രമാണപ്പെട്ടു';

  @override
  String get country => 'രാജ്യം';

  @override
  String get registerStatus => 'രജിസ്റ്റർ സ്ഥിതി';

  @override
  String get registerMethod => 'രജിസ്റ്റർ മെഥോഡ്';

  @override
  String get banTo => 'നിഷേധിച്ചിടാൻ';

  @override
  String get deletedAt => 'ഇല്ലാതാക്കിയത്';

  @override
  String get createdAt => 'സൃഷ്ടിച്ചത്';

  @override
  String get updatedAt => 'അപ്ഡേറ്റുചെയ്തത്';

  @override
  String get reports => 'റിപ്പോർട്ടുകൾ';

  @override
  String get clickToSeeAllUserDevicesDetails =>
      'എല്ലാ ഉപയോക്താക്കൾക്കും ഉപകരണങ്ങളുടെ വിശദാംശങ്ങൾ കാണുക';

  @override
  String get allDeletedMessages => 'എല്ലാ ഇല്ലാതാക്കിയ സന്ദേശങ്ങൾ';

  @override
  String get voiceCallMessage => 'വോയ്സ് കോൾ സന്ദേശം';

  @override
  String get totalRooms => 'ആകെ റൂമുകൾ';

  @override
  String get directRooms => 'സ്വന്തമായ റൂമുകൾ';

  @override
  String get userAction => 'ഉപയോക്തൃ പ്രവർത്തനം';

  @override
  String get status => 'സ്ഥിതി';

  @override
  String get joinedAt => 'ചേരുകയായത്';

  @override
  String get saveLogin => 'ലോഗിൻ വിവരങ്ങൾ സേവ് ചെയ്യുക';

  @override
  String get passwordIsRequired => 'പാസ്വേഡ് ആവശ്യമാണ്';

  @override
  String get verified => 'പ്രമാണപ്പെട്ടു';

  @override
  String get pending => 'കാത്തിരിക്കുന്നു';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => 'വിവരണം ആവശ്യമാണ്';

  @override
  String get seconds => 'സെക്കൻഡുകൾ';

  @override
  String get clickToSeeAllUserInformations =>
      'എല്ലാ ഉപയോക്താക്കൾക്കും വിവരങ്ങൾ കാണുകയായി ക്ലിക്ക് ചെയ്യുക';

  @override
  String get clickToSeeAllUserCountries =>
      'എല്ലാ ഉപയോക്താക്കൾക്കും രാജ്യങ്ങളുടെ വിശദാംശങ്ങൾ കാണുകയായി ക്ലിക്ക് ചെയ്യുക';

  @override
  String get clickToSeeAllUserMessagesDetails =>
      'എല്ലാ ഉപയോക്താക്കൾക്കും സന്ദേശങ്ങളുടെ വിശദാംശങ്ങൾ കാണുകയായി ക്ലിക്ക് ചെയ്യുക';

  @override
  String get clickToSeeAllUserRoomsDetails =>
      'എല്ലാ ഉപയോക്താക്കൾക്കും റൂമുകളുടെ വിശദാംശങ്ങൾ കാണുകയായി ക്ലിക്ക് ചെയ്യുക';

  @override
  String get clickToSeeAllUserReports => 'Click to see all user reports';

  @override
  String get banAt => 'Ban at';

  @override
  String get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
      'Now you login as read-only admin. All edits you make will not be applied due to this being a test version.';

  @override
  String get createStory => 'കഥ സൃഷ്ടിക്കുക';

  @override
  String get writeACaption => 'ഒരു കപ്ഷൻ എഴുതുക...';

  @override
  String get storyCreatedSuccessfully => 'കഥ വിജയകരമായി സൃഷ്ടിച്ചു';

  @override
  String get stories => 'കഥകൾ';

  @override
  String get clear => 'ശൂന്യമാക്കുക';

  @override
  String get clearCallsConfirm => 'കഴിഞ്ഞുപോയിയ്ക്കുക സ്ഥിരീകരിക്കുക';

  @override
  String get chooseHowAutomaticDownloadWorks =>
      'സ്വയംപ്രാപ്തമായ ഡൗൺലോഡ് പ്രവർത്തിപ്പിക്കുന്ന രീതി തിരഞ്ഞെടുക';

  @override
  String get whenUsingMobileData => 'മൊബൈൽ ഡാറ്റ ഉപയോഗിക്കുമ്പോൾ';

  @override
  String get whenUsingWifi => 'Wi-Fi ഉപയോഗിക്കുമ്പോൾ';

  @override
  String get image => 'ചിത്രം';

  @override
  String get myPrivacy => 'എന്റെ സ്വകാര്യത';

  @override
  String get createTextStory => 'വചന കഥ സൃഷ്ടിക്കുക';

  @override
  String get createMediaStory => 'മീഡിയ കഥ സൃഷ്ടിക്കുക';

  @override
  String get camera => 'ക്യാമറ';

  @override
  String get gallery => 'ഗാലറി';

  @override
  String get recentUpdate => 'സമീപകാല അപ്ഡേറ്റ്';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => 'പുതിയ കഥ ചേർക്കുക';

  @override
  String get updateYourProfile => 'നിന്നെയുടെ പ്രൊഫൈൽ അപ്ഡേറ്റ് ചെയ്യുക';

  @override
  String get configureYourAccountPrivacy =>
      'നിന്നെയുടെ അക്കൗണ്ടിന്റെ സ്വകാര്യത കേടുക';

  @override
  String get youInPublicSearch => 'പൊതു തിരയിൽ നിന്ന്';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      'പൊതു തിരയിൽ നിന്നും ഗ്രൂപ്പുകൾക്ക് ചേർത്ത് നിന്ന് നിന്നും നിന്ന്';

  @override
  String get yourLastSeen => 'നിന്നെക്കുറിച്ചുള്ള അവസാന കാണാൻ';

  @override
  String get yourLastSeenInChats =>
      'ചാറ്റുകളിൽ നിന്നെക്കുറിച്ചുള്ള അവസാന കാണാൻ';

  @override
  String get startNewChatWithYou =>
      'നിന്നെക്കുറിച്ചുള്ള പുതിയ ചാറ്റ് ആരംഭിക്കുക';

  @override
  String get yourStory => 'നിന്നെക്കുറിച്ചുള്ള കഥ';

  @override
  String get forRequest => 'അഭ്യർഥനയ്ക്ക്';

  @override
  String get public => 'പൊതു';

  @override
  String get createYourStory => 'നിന്നെക്കുറിച്ചുള്ള കഥ സൃഷ്ടിക്കുക';

  @override
  String get shareYourStatus => 'നിന്നെക്കുറിച്ചുള്ള സ്റ്റാറ്റസ് പങ്കിടുക';

  @override
  String get oneSeenMessage => 'ഒരു കാണപ്പെട്ട സന്ദേശം';

  @override
  String get messageHasBeenViewed => 'സന്ദേശം കാണപ്പെട്ടു';

  @override
  String get clickToSee => 'കാണുന്നതിന് ക്ലിക്ക് ചെയ്യുക';

  @override
  String get images => 'ചിത്രങ്ങൾ';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String get areYouSureRemoveAccount =>
      'Are you sure you want to remove this account?';

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';

  @override
  String get accountAddedSuccessfully => 'Account added successfully';

  @override
  String get addProfilePicture => 'Add Profile Picture';

  @override
  String get addProfilePictureSubtitle =>
      'Add a profile picture to help others recognize you';

  @override
  String get pleaseSelectProfilePicture => 'Please select a profile picture';

  @override
  String get uploading => 'Uploading...';

  @override
  String get continueText => 'Continue';

  @override
  String get profilePictureRequired =>
      'A profile picture is required to continue. Please select an image to upload.';

  @override
  String get shareProfile => 'Share Profile';

  @override
  String get liveStreams => 'Live Streams';

  @override
  String get goLive => 'Go Live';

  @override
  String get shareYourMomentsLive => 'Share your moments live with friends';

  @override
  String get viewers => 'viewers';

  @override
  String get join => 'Join';

  @override
  String get joinLiveStream => 'Join Live Stream';

  @override
  String get viewDetails => 'View Details';

  @override
  String get startLiveStream => 'Start Live Stream';

  @override
  String get endLiveStream => 'End Live Stream';

  @override
  String get leaveLiveStream => 'Leave Live Stream';

  @override
  String get inviteUsers => 'Invite Users';

  @override
  String get banUser => 'Ban User';

  @override
  String get unbanUser => 'Unban User';

  @override
  String get pinMessage => 'Pin Message';

  @override
  String get unpinMessage => 'Unpin Message';

  @override
  String get sendComment => 'Send Comment';

  @override
  String get addReaction => 'Add Reaction';

  @override
  String get liveStreamTitle => 'Live Stream Title';

  @override
  String get liveStreamDescription => 'Description (optional)';

  @override
  String get whoCanWatch => 'Who can watch?';

  @override
  String get everyone => 'Everyone';

  @override
  String get specificFriends => 'Specific Friends';

  @override
  String get selectFriends => 'Select Friends';

  @override
  String get applyFilters => 'Apply Filters';

  @override
  String get noFilters => 'No Filters';

  @override
  String get startStreaming => 'Start Streaming';

  @override
  String get streamEnded => 'Stream Ended';

  @override
  String get youAreNowLive => 'You are now live!';

  @override
  String get streamEndedByHost => 'Stream ended by host';

  @override
  String get removedFromStream => 'You have been removed from the stream';

  @override
  String get commentPlaceholder => 'Add a comment...';

  @override
  String get pinnedMessage => 'Pinned Message';

  @override
  String get hostControls => 'Host Controls';

  @override
  String get participants => 'Participants';

  @override
  String get comments => 'Comments';

  @override
  String get reactions => 'Reactions';
}
