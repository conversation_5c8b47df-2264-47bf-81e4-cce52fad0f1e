// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get done => 'Fertig';

  @override
  String get loading => 'Laden ...';

  @override
  String get messageHasBeenDeleted => 'Die Nachricht wurde gelöscht';

  @override
  String get mute => 'Stumm';

  @override
  String get cancel => 'Abbrechen';

  @override
  String get typing => 'Schreibt...';

  @override
  String get ok => 'OK';

  @override
  String get recording => 'Aufnahme...';

  @override
  String get connecting => 'Verbinden...';

  @override
  String get deleteYouCopy => 'Deine Kopie löschen';

  @override
  String get unMute => 'Ton an';

  @override
  String get delete => 'Löschen';

  @override
  String get report => 'Melden';

  @override
  String get leaveGroup => 'Gruppe verlassen';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      'Bist du sicher, dass du deine Kopie erlauben möchtest? Diese Aktion kann nicht rückgängig gemacht werden';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      'Bist du sicher, dass du diese Gruppe verlassen möchtest? Diese Aktion kann nicht rückgängig gemacht werden';

  @override
  String get leaveGroupAndDeleteYourMessageCopy =>
      'Gruppe verlassen und deine Nachrichten kopie löschen';

  @override
  String get vMessageInfoTrans => 'Nachrichteninfo';

  @override
  String get updateTitleTo => 'Titel aktualisieren auf';

  @override
  String get updateImage => 'Bild aktualisieren';

  @override
  String get joinedBy => 'Beigetreten von';

  @override
  String get promotedToAdminBy => 'Zum Administrator befördert von';

  @override
  String get dismissedToMemberBy => 'Abgewiesen zu Mitglied von';

  @override
  String get leftTheGroup => 'Hat die Gruppe verlassen';

  @override
  String get you => 'Du';

  @override
  String get kickedBy => 'Rausgeworfen von';

  @override
  String get groupCreatedBy => 'Gruppe erstellt von';

  @override
  String get groupDeletedBy => 'Group deleted by';

  @override
  String get addedYouToNewBroadcast =>
      'Hat dich zu einer neuen Rundsendung hinzugefügt';

  @override
  String get download => 'Herunterladen';

  @override
  String get copy => 'Kopieren';

  @override
  String get info => 'Info';

  @override
  String get share => 'Teilen';

  @override
  String get forward => 'Weiterleiten';

  @override
  String get reply => 'Antworten';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => 'Von allen löschen';

  @override
  String get deleteFromMe => 'Von mir löschen';

  @override
  String get downloading => 'Herunterladen...';

  @override
  String get fileHasBeenSavedTo => 'Datei wurde gespeichert unter';

  @override
  String get online => 'Online';

  @override
  String get youDontHaveAccess => 'Du hast keinen Zugriff';

  @override
  String get replyToYourSelf => 'Antworte auf dich selbst';

  @override
  String get repliedToYourSelf => 'Hat auf dich selbst geantwortet';

  @override
  String get audioCall => 'Audioanruf';

  @override
  String get ring => 'Klingeln';

  @override
  String get canceled => 'Abgebrochen';

  @override
  String get timeout => 'Zeitüberschreitung';

  @override
  String get rejected => 'Abgelehnt';

  @override
  String get finished => 'Beendet';

  @override
  String get inCall => 'Im Anruf';

  @override
  String get sessionEnd => 'Sitzungsende';

  @override
  String get yesterday => 'Gestern';

  @override
  String get today => 'Heute';

  @override
  String get textFieldHint => 'Nachricht eingeben...';

  @override
  String get files => 'Dateien';

  @override
  String get location => 'Standort';

  @override
  String get shareMediaAndLocation => 'Medien und Standort teilen';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize =>
      'Es gibt ein Video, das die erlaubte Größe überschreitet';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize =>
      'Es gibt eine Datei, die die erlaubte Größe überschreitet';

  @override
  String get makeCall => 'Anruf tätigen';

  @override
  String get areYouWantToMakeVideoCall =>
      'Möchtest du einen Videoanruf tätigen?';

  @override
  String get areYouWantToMakeVoiceCall =>
      'Möchtest du einen Sprachanruf tätigen?';

  @override
  String get vMessagesInfoTrans => 'Nachrichteninfo';

  @override
  String get star => 'Markieren';

  @override
  String get minutes => 'Minuten';

  @override
  String get sendMessage => 'Nachricht senden';

  @override
  String get deleteUser => 'Benutzer löschen';

  @override
  String get actions => 'Aktionen';

  @override
  String get youAreAboutToDeleteThisUserFromYourList =>
      'Du bist dabei, diesen Benutzer aus deiner Liste zu löschen';

  @override
  String get updateBroadcastTitle => 'Titel der Rundsendung aktualisieren';

  @override
  String get usersAddedSuccessfully => 'Benutzer erfolgreich hinzugefügt';

  @override
  String get broadcastSettings => 'Rundsendungseinstellungen';

  @override
  String get addParticipants => 'Teilnehmer hinzufügen';

  @override
  String get broadcastParticipants => 'Rundsendungsteilnehmer';

  @override
  String get updateGroupDescription => 'Gruppenbeschreibung aktualisieren';

  @override
  String get updateGroupTitle => 'Gruppentitel aktualisieren';

  @override
  String get groupSettings => 'Gruppeneinstellungen';

  @override
  String get description => 'Beschreibung';

  @override
  String get muteNotifications => 'Benachrichtigungen stummschalten';

  @override
  String get groupParticipants => 'Gruppenteilnehmer';

  @override
  String get blockUser => 'Benutzer blockieren';

  @override
  String get areYouSureToBlock => 'Bist du sicher, dass du blockieren möchtest';

  @override
  String get userPage => 'Benutzerseite';

  @override
  String get starMessage => 'Nachricht markieren';

  @override
  String get showMedia => 'Medien anzeigen';

  @override
  String get reportUser => 'Benutzer melden';

  @override
  String get groupName => 'Gruppenname';

  @override
  String get changeSubject => 'Thema ändern';

  @override
  String get titleIsRequired => 'Titel ist erforderlich';

  @override
  String get createBroadcast => 'Rundsendung erstellen';

  @override
  String get broadcastName => 'Rundsendungsname';

  @override
  String get createGroup => 'Gruppe erstellen';

  @override
  String get forgetPassword => 'Passwort vergessen';

  @override
  String get globalSearch => 'Globale Suche';

  @override
  String get dismissesToMember => 'Abgewiesen zu Mitglied';

  @override
  String get setToAdmin => 'Als Administrator festlegen';

  @override
  String get kickMember => 'Mitglied entfernen';

  @override
  String get youAreAboutToDismissesToMember =>
      'Du stehst kurz davor, ein Mitglied abzuweisen';

  @override
  String get youAreAboutToKick => 'Du stehst kurz davor, zu entfernen';

  @override
  String get groupMembers => 'Gruppenmitglieder';

  @override
  String get tapForPhoto => 'Für Foto tippen';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      'We high recommend to download this update';

  @override
  String get newGroup => 'Neue Gruppe';

  @override
  String get newBroadcast => 'Neue Rundsendung';

  @override
  String get starredMessage => 'Markierte Nachrichten';

  @override
  String get settings => 'Einstellungen';

  @override
  String get chats => 'CHATS';

  @override
  String get recentUpdates => 'Aktualisierungen';

  @override
  String get startChat => 'Chat starten';

  @override
  String get newUpdateIsAvailable => 'Ein neues Update ist verfügbar';

  @override
  String get emailNotValid => 'E-Mail ungültig';

  @override
  String get passwordMustHaveValue => 'Passwort muss einen Wert haben';

  @override
  String get error => 'Fehler';

  @override
  String get password => 'Passwort';

  @override
  String get login => 'Anmelden';

  @override
  String get needNewAccount => 'Benötigen Sie ein neues Konto?';

  @override
  String get register => 'Registrieren';

  @override
  String get nameMustHaveValue => 'Name muss einen Wert haben';

  @override
  String get passwordNotMatch => 'Passwort stimmt nicht überein';

  @override
  String get name => 'Name';

  @override
  String get email => 'E-Mail';

  @override
  String get confirmPassword => 'Passwort bestätigen';

  @override
  String get alreadyHaveAnAccount => 'Bereits ein Konto?';

  @override
  String get logOut => 'Abmelden';

  @override
  String get back => 'Zurück';

  @override
  String get sendCodeToMyEmail => 'Code an meine E-Mail senden';

  @override
  String get invalidLoginData => 'Ungültige Anmeldedaten';

  @override
  String get userEmailNotFound => 'Benutzer-E-Mail nicht gefunden';

  @override
  String get yourAccountBlocked => 'Ihr Konto wurde gesperrt';

  @override
  String get yourAccountDeleted => 'Ihr Konto wurde gelöscht';

  @override
  String get userAlreadyRegister => 'Benutzer bereits registriert';

  @override
  String get codeHasBeenExpired => 'Code ist abgelaufen';

  @override
  String get invalidCode => 'Ungültiger Code';

  @override
  String get whileAuthCanFindYou => 'While authentication cannot find you';

  @override
  String get userRegisterStatusNotAcceptedYet =>
      'Benutzerregistrierungsstatus noch nicht akzeptiert';

  @override
  String get deviceHasBeenLogoutFromAllDevices =>
      'Gerät wurde von allen Geräten abgemeldet';

  @override
  String get userDeviceSessionEndDeviceDeleted =>
      'Sitzung des Benutzergeräts beendet, Gerät gelöscht';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      'No code has been send to you to verify your email';

  @override
  String get roomAlreadyInCall => 'Raum bereits im Anruf';

  @override
  String get peerUserInCallNow => 'Benutzer ist gerade im Anruf';

  @override
  String get callNotAllowed => 'Anruf nicht erlaubt';

  @override
  String get peerUserDeviceOffline => 'Gerät des anderen Benutzers offline';

  @override
  String get emailMustBeValid => 'E-Mail muss gültig sein';

  @override
  String get wait2MinutesToSendMail =>
      'Warte 2 Minuten, um die E-Mail zu senden';

  @override
  String get codeMustEqualToSixNumbers => 'Code muss aus sechs Zahlen bestehen';

  @override
  String get newPasswordMustHaveValue => 'Neues Passwort muss einen Wert haben';

  @override
  String get confirmPasswordMustHaveValue =>
      'Passwortbestätigung muss einen Wert haben';

  @override
  String get congregationsYourAccountHasBeenAccepted =>
      'Glückwunsch, Ihr Konto wurde akzeptiert';

  @override
  String get yourAccountIsUnderReview => 'Ihr Konto wird überprüft';

  @override
  String get waitingList => 'Warteliste';

  @override
  String get welcome => 'Willkommen';

  @override
  String get retry => 'Erneut versuchen';

  @override
  String get deleteMember => 'Mitglied löschen';

  @override
  String get profile => 'Profil';

  @override
  String get broadcastInfo => 'Rundsendungsinfo';

  @override
  String get updateTitle => 'Titel aktualisieren';

  @override
  String get members => 'Mitglieder';

  @override
  String get addMembers => 'Mitglieder hinzufügen';

  @override
  String get success => 'Erfolg';

  @override
  String get media => 'Medien';

  @override
  String get docs => 'Dokumente';

  @override
  String get links => 'Links';

  @override
  String get soon => 'Bald';

  @override
  String get unStar => 'Stern entfernen';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      'Die Aktualisierung der Gruppenbeschreibung wird alle Gruppenmitglieder aktualisieren';

  @override
  String get updateNickname => 'Spitznamen aktualisieren';

  @override
  String get groupInfo => 'Gruppeninfo';

  @override
  String get youNotParticipantInThisGroup =>
      'Sie sind kein Teilnehmer dieser Gruppe';

  @override
  String get search => 'Suche';

  @override
  String get mediaLinksAndDocs => 'Medien, Links und Dokumente';

  @override
  String get starredMessages => 'Markierte Nachrichten';

  @override
  String get nickname => 'Spitzname';

  @override
  String get none => 'Keiner';

  @override
  String get yes => 'Ja';

  @override
  String get no => 'Nein';

  @override
  String get exitGroup => 'Gruppe verlassen';

  @override
  String get clickToAddGroupDescription =>
      'Klicken Sie hier, um eine Gruppenbeschreibung hinzuzufügen';

  @override
  String get unBlockUser => 'Benutzer entsperren';

  @override
  String get areYouSureToUnBlock =>
      'Sind Sie sicher, dass Sie den Benutzer entsperren möchten';

  @override
  String get contactInfo => 'Kontaktinformationen';

  @override
  String get audio => 'Audio';

  @override
  String get video => 'Video';

  @override
  String get hiIamUse => 'Hallo, ich benutze';

  @override
  String get on => 'Ein';

  @override
  String get off => 'Aus';

  @override
  String get unBlock => 'Entsperren';

  @override
  String get block => 'Blockieren';

  @override
  String get chooseAtLestOneMember => 'Choose at lest one member';

  @override
  String get close => 'Schließen';

  @override
  String get next => 'Weiter';

  @override
  String get appMembers => 'App-Mitglieder';

  @override
  String get create => 'Erstellen';

  @override
  String get upgradeToAdmin => 'Zum Administrator befördern';

  @override
  String get update => 'Aktualisieren';

  @override
  String get deleteChat => 'Chat löschen';

  @override
  String get clearChat => 'Chat leeren';

  @override
  String get showHistory => 'Verlauf anzeigen';

  @override
  String get groupIcon => 'Gruppensymbol';

  @override
  String get tapToSelectAnIcon => 'Tippen Sie, um ein Symbol auszuwählen';

  @override
  String get groupDescription => 'Gruppenbeschreibung';

  @override
  String get more => 'Mehr';

  @override
  String get messageInfo => 'Nachrichteninfo';

  @override
  String get successfullyDownloadedIn => 'Erfolgreich heruntergeladen in';

  @override
  String get delivered => 'Zugestellt';

  @override
  String get read => 'Gelesen';

  @override
  String get orLoginWith => 'Oder anmelden mit';

  @override
  String get resetPassword => 'Passwort zurücksetzen';

  @override
  String get otpCode => 'OTP-Code';

  @override
  String get newPassword => 'Neues Passwort';

  @override
  String get areYouSure => 'Sind Sie sicher?';

  @override
  String get broadcastMembers => 'Rundsendungsteilnehmer';

  @override
  String get phone => 'Telefon';

  @override
  String get users => 'Benutzer';

  @override
  String get calls => 'Anrufe';

  @override
  String get yourAreAboutToLogoutFromThisAccount =>
      'Sie stehen kurz davor, sich von diesem Konto abzumelden';

  @override
  String get noUpdatesAvailableNow => 'Derzeit sind keine Updates verfügbar';

  @override
  String get dataPrivacy => 'Datenschutz';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      'Alle Daten wurden gesichert. Sie müssen die Daten nicht selbst verwalten. Wenn Sie sich abmelden und erneut anmelden, werden alle Chats angezeigt, ebenso wie in der Webversion';

  @override
  String get account => 'Konto';

  @override
  String get linkedDevices => 'Verknüpfte Geräte';

  @override
  String get storageAndData => 'Speicher und Daten';

  @override
  String get tellAFriend => 'Freund erzählen';

  @override
  String get help => 'Hilfe';

  @override
  String get blockedUsers => 'Blockierte Benutzer';

  @override
  String get inAppAlerts => 'In-App-Benachrichtigungen';

  @override
  String get language => 'Sprache';

  @override
  String get adminNotification => 'Admin-Benachrichtigung';

  @override
  String get checkForUpdates => 'Nach Updates suchen';

  @override
  String get linkByQrCode => 'Über QR-Code verknüpfen';

  @override
  String get deviceStatus => 'Gerätestatus';

  @override
  String get desktopAndOtherDevices => 'Desktop und andere Geräte';

  @override
  String get linkADeviceSoon => 'Ein Gerät verknüpfen (bald)';

  @override
  String get lastActiveFrom => 'Zuletzt aktiv ab';

  @override
  String get tapADeviceToEditOrLogOut =>
      'Tippen Sie auf ein Gerät, um es zu bearbeiten oder abzumelden.';

  @override
  String get contactUs => 'Kontaktiere uns';

  @override
  String get supportChatSoon => 'Support-Chat (bald)';

  @override
  String get updateYourName => 'Ihren Namen aktualisieren';

  @override
  String get updateYourBio => 'Ihre Biografie aktualisieren';

  @override
  String get edit => 'Bearbeiten';

  @override
  String get about => 'Über';

  @override
  String get oldPassword => 'Altes Passwort';

  @override
  String get deleteMyAccount => 'Mein Konto löschen';

  @override
  String get passwordHasBeenChanged => 'Passwort wurde geändert';

  @override
  String get logoutFromAllDevices => 'Von allen Geräten abmelden?';

  @override
  String get updateYourPassword => 'Ihr Passwort aktualisieren';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      'Geben Sie Ihren Namen ein und fügen Sie ein optionales Profilbild hinzu';

  @override
  String get privacyPolicy => 'Datenschutzrichtlinie';

  @override
  String get chat => 'Chat';

  @override
  String get send => 'Senden';

  @override
  String get reportHasBeenSubmitted => 'Ihre Meldung wurde gesendet';

  @override
  String get offline => 'Offline';

  @override
  String get harassmentOrBullyingDescription =>
      'Belästigung oder Mobbing: Diese Option ermöglicht es Benutzern, Personen zu melden, die sie oder andere mit belästigenden Nachrichten, Drohungen oder anderen Formen von Mobbing ansprechen.';

  @override
  String get spamOrScamDescription =>
      'Spam oder Betrug: Diese Option ist für Benutzer gedacht, die Konten melden möchten, die Spam-Nachrichten, unerwünschte Werbung senden oder versuchen, andere zu betrügen.';

  @override
  String get areYouSureToReportUserToAdmin =>
      'Sind Sie sicher, dass Sie diesen Benutzer dem Administrator melden möchten?';

  @override
  String get groupWith => 'Gruppe mit';

  @override
  String get inappropriateContentDescription =>
      'Unangemessene Inhalte: Benutzer können diese Option auswählen, um sexuell explizites Material, Hassrede oder andere Inhalte zu melden, die gegen Gemeinschaftsstandards verstoßen.';

  @override
  String get otherCategoryDescription =>
      'Andere: Diese allgemeine Kategorie kann für Verstöße verwendet werden, die nicht leicht in die obigen Kategorien passen. Es könnte hilfreich sein, ein Textfeld bereitzustellen, in dem Benutzer weitere Details angeben können.';

  @override
  String get explainWhatHappens => 'Erklären Sie hier, was passiert';

  @override
  String get loginAgain => 'Erneut anmelden!';

  @override
  String get yourSessionIsEndedPleaseLoginAgain =>
      'Ihre Sitzung wurde beendet. Bitte melden Sie sich erneut an!';

  @override
  String get aboutToBlockUserWithConsequences =>
      'Sie stehen kurz davor, diesen Benutzer zu blockieren. Sie können ihm keine Nachrichten senden und ihn nicht zu Gruppen oder Rundsendungen hinzufügen!';

  @override
  String get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
      'Sie stehen kurz davor, Ihr Konto zu löschen. Ihr Konto wird nicht mehr in der Benutzerliste angezeigt.';

  @override
  String get admin => 'Admin';

  @override
  String get member => 'Mitglied';

  @override
  String get creator => 'Ersteller';

  @override
  String get currentDevice => 'Aktuelles Gerät';

  @override
  String get visits => 'Besuche';

  @override
  String get chooseRoom => 'Raum auswählen';

  @override
  String get deleteThisDeviceDesc =>
      'Das Löschen dieses Geräts führt zur sofortigen Abmeldung dieses Geräts';

  @override
  String get youAreAboutToUpgradeToAdmin =>
      'Du stehst kurz davor, zum Administrator befördert zu werden';

  @override
  String get microphonePermissionMustBeAccepted =>
      'Microphone permission must be accepted';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'Microphone and camera permission must be accepted';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      'Derzeit ist die Anmeldung nicht möglich. Bitte versuchen Sie es später erneut.';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get notification => 'Benachrichtigung';

  @override
  String get total => 'Gesamt';

  @override
  String get blocked => 'Blockiert';

  @override
  String get deleted => 'Gelöscht';

  @override
  String get accepted => 'Akzeptiert';

  @override
  String get notAccepted => 'Nicht akzeptiert';

  @override
  String get web => 'Web';

  @override
  String get android => 'Android';

  @override
  String get macOs => 'macOS';

  @override
  String get windows => 'Windows';

  @override
  String get other => 'Andere';

  @override
  String get totalVisits => 'Gesamtbesuche';

  @override
  String get totalMessages => 'Gesamtnachrichten';

  @override
  String get textMessages => 'Textnachrichten';

  @override
  String get imageMessages => 'Bildnachrichten';

  @override
  String get videoMessages => 'Videonachrichten';

  @override
  String get voiceMessages => 'Sprachnachrichten';

  @override
  String get fileMessages => 'Dateinachrichten';

  @override
  String get infoMessages => 'Informationsnachrichten';

  @override
  String get voiceCallMessages => 'Anrufnachrichten';

  @override
  String get videoCallMessages => 'Videoanrufnachrichten';

  @override
  String get locationMessages => 'Ortsnachrichten';

  @override
  String get directChat => 'Direkter Chat';

  @override
  String get group => 'Gruppe';

  @override
  String get broadcast => 'Rundsenden';

  @override
  String get messageCounter => 'Nachrichtenzähler';

  @override
  String get roomCounter => 'Raumzähler';

  @override
  String get countries => 'Länder';

  @override
  String get devices => 'Geräte';

  @override
  String get notificationTitle => 'Benachrichtigungstitel';

  @override
  String get notificationDescription => 'Benachrichtigungsbeschreibung';

  @override
  String get notificationsPage => 'Benachrichtigungsseite';

  @override
  String get updateFeedBackEmail => 'Feedback-E-Mail aktualisieren';

  @override
  String get setMaxMessageForwardAndShare =>
      'Maximale Nachrichtenweiterleitung und -freigabe festlegen';

  @override
  String get setNewPrivacyPolicyUrl =>
      'Neue Datenschutzrichtlinien-URL festlegen';

  @override
  String get forgetPasswordExpireTime =>
      'Ablaufzeit für das Zurücksetzen des Passworts';

  @override
  String get callTimeoutInSeconds => 'Anrufzeitüberschreitung (in Sekunden)';

  @override
  String get setMaxGroupMembers => 'Maximale Gruppenmitglieder festlegen';

  @override
  String get setMaxBroadcastMembers => 'Maximale Rundsendemitglieder festlegen';

  @override
  String get allowCalls => 'Anrufe erlauben';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      'Wenn diese Option aktiviert ist, sind Video- und Sprachanrufe erlaubt';

  @override
  String get allowAds => 'Werbeanzeigen erlauben';

  @override
  String get allowMobileLogin => 'Mobile Anmeldung erlauben';

  @override
  String get allowWebLogin => 'Webanmeldung erlauben';

  @override
  String get messages => 'Nachrichten';

  @override
  String get appleStoreAppUrl => 'Apple Store App URL';

  @override
  String get googlePlayAppUrl => 'Google Play App URL';

  @override
  String get privacyUrl => 'Datenschutz URL';

  @override
  String get feedBackEmail => 'Feedback-E-Mail';

  @override
  String get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
      'Wenn diese Option deaktiviert ist, werden das Senden von Chatdateien, Bildern, Videos und Standort blockiert';

  @override
  String get allowSendMedia => 'Senden von Medien erlauben';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      'Wenn diese Option deaktiviert ist, wird das Erstellen von Chat-Rundsenden blockiert';

  @override
  String get allowCreateBroadcast => 'Erstellen von Rundsenden erlauben';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      'Wenn diese Option deaktiviert ist, wird das Erstellen von Chatgruppen blockiert';

  @override
  String get allowCreateGroups => 'Erstellen von Gruppen erlauben';

  @override
  String get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
      'Wenn diese Option deaktiviert ist, wird die Desktop-Anmeldung oder -Registrierung (Windows und macOS) blockiert';

  @override
  String get allowDesktopLogin => 'Desktop-Anmeldung erlauben';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      'Wenn diese Option deaktiviert ist, wird die Web-Anmeldung oder -Registrierung blockiert';

  @override
  String get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
      'Wenn diese Option aktiviert ist, werden Google Ads-Banner in Chats angezeigt';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'If this option is enabled, the Google Ads banner will appear in chats.';

  @override
  String get userProfile => 'Benutzerprofil';

  @override
  String get userInfo => 'Benutzerinformationen';

  @override
  String get fullName => 'Vollständiger Name';

  @override
  String get bio => 'Biografie';

  @override
  String get noBio => 'Keine Biografie';

  @override
  String get verifiedAt => 'Verifiziert am';

  @override
  String get country => 'Land';

  @override
  String get registerStatus => 'Registrierungsstatus';

  @override
  String get registerMethod => 'Registrierungsmethode';

  @override
  String get banTo => 'Gebannt bis';

  @override
  String get deletedAt => 'Gelöscht am';

  @override
  String get createdAt => 'Erstellt am';

  @override
  String get updatedAt => 'Aktualisiert am';

  @override
  String get reports => 'Berichte';

  @override
  String get clickToSeeAllUserDevicesDetails =>
      'Klicken Sie hier, um alle Benutzergerätedetails anzuzeigen';

  @override
  String get allDeletedMessages => 'Alle gelöschten Nachrichten';

  @override
  String get voiceCallMessage => 'Sprachanrufnachricht';

  @override
  String get totalRooms => 'Gesamträume';

  @override
  String get directRooms => 'Direkträume';

  @override
  String get userAction => 'Benutzeraktion';

  @override
  String get status => 'Status';

  @override
  String get joinedAt => 'Beigetreten am';

  @override
  String get saveLogin => 'Anmeldung speichern';

  @override
  String get passwordIsRequired => 'Passwort ist erforderlich';

  @override
  String get verified => 'Verifiziert';

  @override
  String get pending => 'Ausstehend';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => 'Beschreibung ist erforderlich';

  @override
  String get seconds => 'Sekunden';

  @override
  String get clickToSeeAllUserInformations =>
      'Klicken Sie hier, um alle Benutzerinformationen anzuzeigen';

  @override
  String get clickToSeeAllUserCountries =>
      'Klicken Sie hier, um alle Benutzländer anzuzeigen';

  @override
  String get clickToSeeAllUserMessagesDetails =>
      'Klicken Sie hier, um alle Benutzernachrichtendetails anzuzeigen';

  @override
  String get clickToSeeAllUserRoomsDetails =>
      'Klicken Sie hier, um alle Benutzerraumdetails anzuzeigen';

  @override
  String get clickToSeeAllUserReports =>
      'Klicken Sie hier, um alle Benutzerberichte anzuzeigen';

  @override
  String get banAt => 'Gebannt am';

  @override
  String get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
      'Sie sind jetzt als schreibgeschützter Administrator angemeldet. Alle Änderungen, die Sie vornehmen, werden nicht übernommen, da dies eine Testversion ist.';

  @override
  String get createStory => 'Story erstellen';

  @override
  String get writeACaption => 'Schreibe eine Bildunterschrift...';

  @override
  String get storyCreatedSuccessfully => 'Story erfolgreich erstellt';

  @override
  String get stories => 'Geschichten';

  @override
  String get clear => 'Löschen';

  @override
  String get clearCallsConfirm => 'Anrufe löschen bestätigen';

  @override
  String get chooseHowAutomaticDownloadWorks =>
      'Wählen Sie aus, wie der automatische Download funktioniert';

  @override
  String get whenUsingMobileData => 'Bei Verwendung von mobilen Daten';

  @override
  String get whenUsingWifi => 'Bei Verwendung von WLAN';

  @override
  String get image => 'Bild';

  @override
  String get myPrivacy => 'Meine Privatsphäre';

  @override
  String get createTextStory => 'Text-Story erstellen';

  @override
  String get createMediaStory => 'Mediengeschichte erstellen';

  @override
  String get camera => 'Kamera';

  @override
  String get gallery => 'Galerie';

  @override
  String get recentUpdate => 'Letzte Aktualisierung';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => 'Neue Story hinzufügen';

  @override
  String get updateYourProfile => 'Aktualisieren Sie Ihr Profil';

  @override
  String get configureYourAccountPrivacy =>
      'Konfigurieren Sie die Privatsphäre Ihres Kontos';

  @override
  String get youInPublicSearch => 'Sie in der öffentlichen Suche';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      'Ihr Profil wird in der öffentlichen Suche angezeigt und für Gruppen hinzugefügt';

  @override
  String get yourLastSeen => 'Zuletzt gesehen';

  @override
  String get yourLastSeenInChats => 'Zuletzt gesehen in Chats';

  @override
  String get startNewChatWithYou => 'Neuen Chat mit dir beginnen';

  @override
  String get yourStory => 'Deine Geschichte';

  @override
  String get forRequest => 'Auf Anfrage';

  @override
  String get public => 'Öffentlich';

  @override
  String get createYourStory => 'Erstelle deine Geschichte';

  @override
  String get shareYourStatus => 'Teile deinen Status';

  @override
  String get oneSeenMessage => 'Eine gesehene Nachricht';

  @override
  String get messageHasBeenViewed => 'Nachricht wurde angezeigt';

  @override
  String get clickToSee => 'Klicken, um zu sehen';

  @override
  String get images => 'Bilder';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String get areYouSureRemoveAccount =>
      'Are you sure you want to remove this account?';

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';

  @override
  String get accountAddedSuccessfully => 'Account added successfully';

  @override
  String get addProfilePicture => 'Add Profile Picture';

  @override
  String get addProfilePictureSubtitle =>
      'Add a profile picture to help others recognize you';

  @override
  String get pleaseSelectProfilePicture => 'Please select a profile picture';

  @override
  String get uploading => 'Uploading...';

  @override
  String get continueText => 'Continue';

  @override
  String get profilePictureRequired =>
      'A profile picture is required to continue. Please select an image to upload.';

  @override
  String get shareProfile => 'Share Profile';

  @override
  String get liveStreams => 'Live Streams';

  @override
  String get goLive => 'Go Live';

  @override
  String get shareYourMomentsLive => 'Share your moments live with friends';

  @override
  String get viewers => 'viewers';

  @override
  String get join => 'Join';

  @override
  String get joinLiveStream => 'Join Live Stream';

  @override
  String get viewDetails => 'View Details';

  @override
  String get startLiveStream => 'Start Live Stream';

  @override
  String get endLiveStream => 'End Live Stream';

  @override
  String get leaveLiveStream => 'Leave Live Stream';

  @override
  String get inviteUsers => 'Invite Users';

  @override
  String get banUser => 'Ban User';

  @override
  String get unbanUser => 'Unban User';

  @override
  String get pinMessage => 'Pin Message';

  @override
  String get unpinMessage => 'Unpin Message';

  @override
  String get sendComment => 'Send Comment';

  @override
  String get addReaction => 'Add Reaction';

  @override
  String get liveStreamTitle => 'Live Stream Title';

  @override
  String get liveStreamDescription => 'Description (optional)';

  @override
  String get whoCanWatch => 'Who can watch?';

  @override
  String get everyone => 'Everyone';

  @override
  String get specificFriends => 'Specific Friends';

  @override
  String get selectFriends => 'Select Friends';

  @override
  String get applyFilters => 'Apply Filters';

  @override
  String get noFilters => 'No Filters';

  @override
  String get startStreaming => 'Start Streaming';

  @override
  String get streamEnded => 'Stream Ended';

  @override
  String get youAreNowLive => 'You are now live!';

  @override
  String get streamEndedByHost => 'Stream ended by host';

  @override
  String get removedFromStream => 'You have been removed from the stream';

  @override
  String get commentPlaceholder => 'Add a comment...';

  @override
  String get pinnedMessage => 'Pinned Message';

  @override
  String get hostControls => 'Host Controls';

  @override
  String get participants => 'Participants';

  @override
  String get comments => 'Comments';

  @override
  String get reactions => 'Reactions';
}
