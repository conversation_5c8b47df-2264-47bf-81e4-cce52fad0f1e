// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:developer';

import 'package:get_it/get_it.dart';

import '../../../core/api_service/live_stream/live_stream_api_service.dart';
import '../../../core/models/live_stream/live_stream_dto.dart';
import '../../../core/models/live_stream/live_stream_model.dart';
import '../../../core/utils/enums.dart';

class LiveStreamApiTest {
  static final _apiService = GetIt.I.get<LiveStreamApiService>();

  /// Test all live streaming API endpoints
  static Future<void> runAllTests() async {
    log('🧪 Starting Live Stream API Tests...');

    try {
      // Test 1: Get active live streams
      await testGetActiveLiveStreams();

      // Test 2: Create live stream
      final liveStreamId = await testCreateLiveStream();

      if (liveStreamId != null) {
        // Test 3: Join live stream
        await testJoinLiveStream(liveStreamId);

        // Test 4: Get participants
        await testGetParticipants(liveStreamId);

        // Test 5: Send comment
        await testSendComment(liveStreamId);

        // Test 6: Get comments
        await testGetComments(liveStreamId);

        // Test 7: Send reaction
        await testSendReaction(liveStreamId);

        // Test 8: Get reactions
        await testGetReactions(liveStreamId);

        // Test 9: Get Agora token
        await testGetAgoraToken(liveStreamId);

        // Test 10: Pin message
        await testPinMessage(liveStreamId);

        // Test 11: Leave live stream
        await testLeaveLiveStream(liveStreamId);

        // Test 12: End live stream
        await testEndLiveStream(liveStreamId);
      }

      log('✅ All Live Stream API Tests Completed Successfully!');
    } catch (e) {
      log('❌ Live Stream API Tests Failed: $e');
    }
  }

  static Future<void> testGetActiveLiveStreams() async {
    log('📡 Testing: Get Active Live Streams');
    try {
      final response = await _apiService.getActiveLiveStreams();
      log('✅ Get Active Live Streams: ${response.values.length} streams found');
    } catch (e) {
      log('❌ Get Active Live Streams failed: $e');
    }
  }

  static Future<String?> testCreateLiveStream() async {
    log('📡 Testing: Create Live Stream');
    try {
      final dto = CreateLiveStreamDto(
        title: 'Test Live Stream ${DateTime.now().millisecondsSinceEpoch}',
        description: 'This is a test live stream created by API test',
        privacy: LiveStreamPrivacy.public,
        allowedUsers: null,
        filters: null,
      );

      final response = await _apiService.startLiveStream(dto);
      final liveStream = response['liveStream'] as LiveStreamModel;
      log('✅ Create Live Stream: ${liveStream.id}');
      return liveStream.id;
    } catch (e) {
      log('❌ Create Live Stream failed: $e');
      return null;
    }
  }

  static Future<void> testJoinLiveStream(String liveStreamId) async {
    log('📡 Testing: Join Live Stream');
    try {
      final response = await _apiService.joinLiveStream(liveStreamId);
      log('✅ Join Live Stream: ${response.id}');
    } catch (e) {
      log('❌ Join Live Stream failed: $e');
    }
  }

  static Future<void> testGetParticipants(String liveStreamId) async {
    log('📡 Testing: Get Participants');
    try {
      final response = await _apiService.getParticipants(liveStreamId);
      log('✅ Get Participants: ${response.values.length} participants');
    } catch (e) {
      log('❌ Get Participants failed: $e');
    }
  }

  static Future<void> testSendComment(String liveStreamId) async {
    log('📡 Testing: Send Comment');
    try {
      final dto = SendCommentDto(
        liveStreamId: liveStreamId,
        message:
            'Test comment from API test ${DateTime.now().millisecondsSinceEpoch}',
      );

      final response = await _apiService.addComment(dto);
      log('✅ Send Comment: ${response.id}');
    } catch (e) {
      log('❌ Send Comment failed: $e');
    }
  }

  static Future<void> testGetComments(String liveStreamId) async {
    log('📡 Testing: Get Comments');
    try {
      final response = await _apiService.getComments(liveStreamId);
      log('✅ Get Comments: ${response.values.length} comments');
    } catch (e) {
      log('❌ Get Comments failed: $e');
    }
  }

  static Future<void> testSendReaction(String liveStreamId) async {
    log('📡 Testing: Send Reaction');
    try {
      final dto = SendReactionDto(
        liveStreamId: liveStreamId,
        reactionType: LiveStreamReactionType.heart,
      );

      final response = await _apiService.addReaction(dto);
      log('✅ Send Reaction: ${response.id}');
    } catch (e) {
      log('❌ Send Reaction failed: $e');
    }
  }

  static Future<void> testGetReactions(String liveStreamId) async {
    log('📡 Testing: Get Reactions');
    try {
      final response = await _apiService.getReactions(liveStreamId);
      final reactions = response['reactions'];
      final stats = response['stats'];
      log('✅ Get Reactions: ${reactions.values.length} reactions, ${stats.totalReactions} total');
    } catch (e) {
      log('❌ Get Reactions failed: $e');
    }
  }

  static Future<void> testGetAgoraToken(String liveStreamId) async {
    log('📡 Testing: Get Agora Token');
    try {
      final response = await _apiService.getAgoraToken(liveStreamId);
      log('✅ Get Agora Token: UID ${response.uid}, expires in ${response.timeUntilExpiration.inMinutes} minutes');
    } catch (e) {
      log('❌ Get Agora Token failed: $e');
    }
  }

  static Future<void> testPinMessage(String liveStreamId) async {
    log('📡 Testing: Pin Message');
    try {
      final dto = PinMessageDto(
        liveStreamId: liveStreamId,
        message: 'Test pinned message from API test',
      );

      final response = await _apiService.pinMessage(dto);
      log('✅ Pin Message: ${response.message}');
    } catch (e) {
      log('❌ Pin Message failed: $e');
    }
  }

  static Future<void> testLeaveLiveStream(String liveStreamId) async {
    log('📡 Testing: Leave Live Stream');
    try {
      await _apiService.leaveLiveStream(liveStreamId);
      log('✅ Leave Live Stream: Success');
    } catch (e) {
      log('❌ Leave Live Stream failed: $e');
    }
  }

  static Future<void> testEndLiveStream(String liveStreamId) async {
    log('📡 Testing: End Live Stream');
    try {
      await _apiService.endLiveStream(liveStreamId);
      log('✅ End Live Stream: Success');
    } catch (e) {
      log('❌ End Live Stream failed: $e');
    }
  }

  /// Test individual endpoint
  static Future<void> testEndpoint(String endpointName) async {
    switch (endpointName.toLowerCase()) {
      case 'getactivelivestreams':
        await testGetActiveLiveStreams();
        break;
      case 'createlivestream':
        await testCreateLiveStream();
        break;
      default:
        log('❌ Unknown endpoint: $endpointName');
    }
  }
}
