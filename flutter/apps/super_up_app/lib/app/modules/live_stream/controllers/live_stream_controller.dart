// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

import '../../../core/api_service/live_stream/live_stream_api_service.dart';
import '../../../core/models/live_stream/live_stream_model.dart';
import '../../../core/models/live_stream/live_stream_participant_model.dart';
import '../../../core/models/live_stream/live_stream_comment_model.dart';
import '../../../core/models/live_stream/live_stream_reaction_model.dart';
import '../../../core/models/live_stream/live_stream_dto.dart';
import '../../../core/models/live_stream/agora_token_response.dart' as agora;
import '../../../core/utils/enums.dart';

class LiveStreamState {
  List<LiveStreamModel> activeLiveStreams = [];
  LiveStreamModel? currentLiveStream;
  List<LiveStreamParticipantModel> participants = [];
  List<LiveStreamCommentModel> comments = [];
  List<LiveStreamReactionModel> recentReactions = [];
  LiveStreamReactionStats? reactionStats;
  agora.AgoraTokenResponse? agoraToken;
  LiveStreamParticipantModel? myParticipant;

  // UI States
  bool isLoadingStreams = false;
  bool isLoadingComments = false;
  bool isLoadingParticipants = false;
  bool isJoining = false;
  bool isLeaving = false;
  bool isSendingComment = false;
  bool isSendingReaction = false;

  // Pagination
  bool hasMoreStreams = true;
  bool hasMoreComments = true;
  bool hasMoreParticipants = true;
  int currentStreamPage = 1;
  int currentCommentPage = 1;
  int currentParticipantPage = 1;

  // Stream status
  bool get isInLiveStream => currentLiveStream != null && myParticipant != null;
  bool get isHost => myParticipant?.isHost ?? false;
  bool get isViewer => myParticipant?.isViewer ?? false;
  bool get canManageStream => isHost;

  // Helper methods
  int get viewerCount => currentLiveStream?.viewerCount ?? 0;
  String get streamTitle => currentLiveStream?.title ?? '';
  bool get hasActiveLiveStreams => activeLiveStreams.isNotEmpty;
}

class LiveStreamController extends SLoadingController<LiveStreamState> {
  LiveStreamController() : super(SLoadingState(LiveStreamState()));

  final _apiService = GetIt.I.get<LiveStreamApiService>();
  Timer? _refreshTimer;
  StreamSubscription? _socketSubscription;

  @override
  void onInit() {
    loadActiveLiveStreams();
    _startPeriodicRefresh();
    _initializeSocketListeners();
  }

  @override
  void onClose() {
    _refreshTimer?.cancel();
    _socketSubscription?.cancel();
    _cleanupSocketListeners();
  }

  void _cleanupSocketListeners() {
    try {
      final socket = VChatController.I.nativeApi.remote.socketIo.socket;

      // Remove live stream event listeners
      socket.off('v1OnLiveStreamStarted');
      socket.off('v1OnLiveStreamEnded');
      socket.off('v1OnLiveStreamUserJoined');
      socket.off('v1OnLiveStreamUserLeft');
      socket.off('v1OnLiveStreamNewComment');
      socket.off('v1OnLiveStreamNewReaction');
      socket.off('v1OnLiveStreamUserBanned');
      socket.off('v1OnLiveStreamMessagePinned');
      socket.off('v1OnLiveStreamViewerCountUpdated');

      debugPrint('Live stream socket listeners cleaned up');
    } catch (e) {
      debugPrint('Failed to cleanup socket listeners: $e');
    }
  }

  // Initialize socket listeners for real-time updates
  void _initializeSocketListeners() {
    try {
      final socket = VChatController.I.nativeApi.remote.socketIo.socket;

      // Listen for live stream events
      socket.on('v1OnLiveStreamStarted',
          (data) => _handleLiveStreamStarted(jsonDecode(data.toString())));
      socket.on('v1OnLiveStreamEnded',
          (data) => _handleLiveStreamEnded(jsonDecode(data.toString())));
      socket.on('v1OnLiveStreamUserJoined',
          (data) => _handleUserJoined(jsonDecode(data.toString())));
      socket.on('v1OnLiveStreamUserLeft',
          (data) => _handleUserLeft(jsonDecode(data.toString())));
      socket.on('v1OnLiveStreamNewComment',
          (data) => _handleNewComment(jsonDecode(data.toString())));
      socket.on('v1OnLiveStreamNewReaction',
          (data) => _handleNewReaction(jsonDecode(data.toString())));
      socket.on('v1OnLiveStreamUserBanned',
          (data) => _handleUserBanned(jsonDecode(data.toString())));
      socket.on('v1OnLiveStreamMessagePinned',
          (data) => _handleMessagePinned(jsonDecode(data.toString())));
      socket.on('v1OnLiveStreamViewerCountUpdated',
          (data) => _handleViewerCountUpdated(jsonDecode(data.toString())));

      debugPrint('Live stream socket listeners initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize socket listeners: $e');
      // Fallback to periodic refresh if socket fails
    }
  }

  void _handleLiveStreamStarted(Map<String, dynamic> data) {
    try {
      if (data['liveStream'] == null) {
        debugPrint('Invalid live stream data received');
        return;
      }

      final liveStream = LiveStreamModel.fromMap(data['liveStream']);

      // Check if stream already exists to avoid duplicates
      final existingIndex =
          value.data.activeLiveStreams.indexWhere((s) => s.id == liveStream.id);
      if (existingIndex == -1) {
        value.data.activeLiveStreams.insert(0, liveStream);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error handling live stream started: $e');
    }
  }

  void _handleLiveStreamEnded(Map<String, dynamic> data) {
    try {
      final liveStreamId = data['liveStreamId'] as String?;
      if (liveStreamId == null || liveStreamId.isEmpty) {
        debugPrint('Invalid live stream ID received for stream end');
        return;
      }

      value.data.activeLiveStreams
          .removeWhere((stream) => stream.id == liveStreamId);

      // If we're in this stream, handle cleanup
      if (value.data.currentLiveStream?.id == liveStreamId) {
        _cleanupCurrentStream();
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error handling live stream ended: $e');
    }
  }

  void _handleUserJoined(Map<String, dynamic> data) {
    try {
      final liveStreamId = data['liveStreamId'] as String?;
      if (liveStreamId == null ||
          value.data.currentLiveStream?.id != liveStreamId) {
        return; // Not our current stream
      }

      if (data['participant'] == null) {
        debugPrint('Invalid participant data received');
        return;
      }

      final participant =
          LiveStreamParticipantModel.fromMap(data['participant']);

      // Check if participant already exists to avoid duplicates
      final existingIndex = value.data.participants
          .indexWhere((p) => p.userId == participant.userId);
      if (existingIndex == -1) {
        value.data.participants.add(participant);
      }

      // Update viewer count
      if (data['viewerCount'] != null) {
        final viewerCount = data['viewerCount'] as int?;
        if (viewerCount != null && viewerCount >= 0) {
          value.data.currentLiveStream = value.data.currentLiveStream?.copyWith(
            viewerCount: viewerCount,
          );
        }
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error handling user joined: $e');
    }
  }

  void _handleUserLeft(Map<String, dynamic> data) {
    try {
      final liveStreamId = data['liveStreamId'] as String?;
      if (liveStreamId == null ||
          value.data.currentLiveStream?.id != liveStreamId) {
        return; // Not our current stream
      }

      final userId = data['userId'] as String?;
      if (userId == null || userId.isEmpty) {
        debugPrint('Invalid user ID received for user left');
        return;
      }

      value.data.participants.removeWhere((p) => p.userId == userId);

      // Update viewer count
      if (data['viewerCount'] != null) {
        final viewerCount = data['viewerCount'] as int?;
        if (viewerCount != null && viewerCount >= 0) {
          value.data.currentLiveStream = value.data.currentLiveStream?.copyWith(
            viewerCount: viewerCount,
          );
        }
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error handling user left: $e');
    }
  }

  void _handleNewComment(Map<String, dynamic> data) {
    try {
      final liveStreamId = data['liveStreamId'] as String?;
      if (liveStreamId == null ||
          value.data.currentLiveStream?.id != liveStreamId) {
        return; // Not our current stream
      }

      if (data['comment'] == null) {
        debugPrint('Invalid comment data received');
        return;
      }

      final comment = LiveStreamCommentModel.fromMap(data['comment']);

      // Check for duplicate comments
      final existingIndex =
          value.data.comments.indexWhere((c) => c.id == comment.id);
      if (existingIndex == -1) {
        value.data.comments.insert(0, comment);

        // Keep only recent comments to avoid memory issues
        if (value.data.comments.length > 100) {
          value.data.comments = value.data.comments.take(100).toList();
        }
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error handling new comment: $e');
    }
  }

  void _handleNewReaction(Map<String, dynamic> data) {
    if (value.data.currentLiveStream?.id == data['liveStreamId']) {
      final reaction = LiveStreamReactionModel.fromMap(data['reaction']);
      value.data.recentReactions.insert(0, reaction);

      // Keep only recent reactions
      if (value.data.recentReactions.length > 50) {
        value.data.recentReactions =
            value.data.recentReactions.take(50).toList();
      }
      notifyListeners();
    }
  }

  void _handleUserBanned(Map<String, dynamic> data) {
    if (value.data.currentLiveStream?.id == data['liveStreamId']) {
      final userId = data['userId'] as String;

      // If I'm the one being banned, leave the stream
      if (userId == AppAuth.myId) {
        _cleanupCurrentStream();
        VAppAlert.showErrorSnackBar(
          message: 'You have been removed from the live stream',
          context: VChatController.I.navigationContext,
        );
      } else {
        // Remove the banned user from participants
        value.data.participants.removeWhere((p) => p.userId == userId);
      }
      notifyListeners();
    }
  }

  void _handleMessagePinned(Map<String, dynamic> data) {
    if (value.data.currentLiveStream?.id == data['liveStreamId']) {
      final pinnedMessage =
          LiveStreamPinnedMessage.fromMap(data['pinnedMessage']);
      value.data.currentLiveStream = value.data.currentLiveStream?.copyWith(
        pinnedMessage: pinnedMessage,
      );
      notifyListeners();
    }
  }

  void _handleViewerCountUpdated(Map<String, dynamic> data) {
    if (value.data.currentLiveStream?.id == data['liveStreamId']) {
      value.data.currentLiveStream = value.data.currentLiveStream?.copyWith(
        viewerCount: data['viewerCount'] as int,
      );
      notifyListeners();
    }
  }

  void _startPeriodicRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (value.data.isInLiveStream) {
        _refreshCurrentStreamData();
      } else {
        loadActiveLiveStreams(refresh: true);
      }
    });
  }

  void _refreshCurrentStreamData() {
    if (value.data.currentLiveStream != null) {
      // Refresh participant count and other live data
      loadParticipants(refresh: true);
    }
  }

  void _cleanupCurrentStream() {
    value.data.currentLiveStream = null;
    value.data.myParticipant = null;
    value.data.participants.clear();
    value.data.comments.clear();
    value.data.recentReactions.clear();
    value.data.agoraToken = null;
  }

  // API Methods

  // Load active live streams
  Future<void> loadActiveLiveStreams({bool refresh = false}) async {
    if (refresh) {
      value.data.currentStreamPage = 1;
      value.data.hasMoreStreams = true;
    }

    if (value.data.isLoadingStreams || !value.data.hasMoreStreams) return;

    await vSafeApiCall(
      onLoading: () async {
        value.data.isLoadingStreams = true;
        notifyListeners();
      },
      onError: (exception, trace) {
        value.data.isLoadingStreams = false;
        notifyListeners();
        VAppAlert.showErrorSnackBar(
          message: exception,
          context: VChatController.I.navigationContext,
        );
      },
      request: () async {
        return _apiService.getActiveLiveStreams(
          page: value.data.currentStreamPage,
          limit: 20,
        );
      },
      onSuccess: (response) {
        value.data.isLoadingStreams = false;

        if (refresh) {
          value.data.activeLiveStreams = response.values;
        } else {
          value.data.activeLiveStreams.addAll(response.values);
        }

        value.data.hasMoreStreams = response.hasNextPage;
        value.data.currentStreamPage++;
        notifyListeners();
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  // Start a new live stream
  Future<void> startLiveStream(CreateLiveStreamDto dto) async {
    await vSafeApiCall(
      onLoading: () async {
        setStateLoading();
      },
      onError: (exception, trace) {
        setStateError();
        // TODO: Show error to user when context is available
        debugPrint('Live stream error: $exception');
      },
      request: () async {
        return _apiService.startLiveStream(dto);
      },
      onSuccess: (liveStream) {
        value.data.currentLiveStream = liveStream;
        value.data.activeLiveStreams.insert(0, liveStream);
        setStateSuccess();
        notifyListeners();
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  // End current live stream
  Future<void> endLiveStream() async {
    if (value.data.currentLiveStream == null) return;

    await vSafeApiCall(
      onLoading: () async {
        // Don't show loading state for ending
      },
      onError: (exception, trace) {
        debugPrint('Live stream error: $exception');
      },
      request: () async {
        return _apiService.endLiveStream(value.data.currentLiveStream!.id);
      },
      onSuccess: (response) {
        _cleanupCurrentStream();
        notifyListeners();
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  // Join a live stream
  Future<void> joinLiveStream(String liveStreamId) async {
    await vSafeApiCall(
      onLoading: () async {
        value.data.isJoining = true;
        notifyListeners();
      },
      onError: (exception, trace) {
        value.data.isJoining = false;
        notifyListeners();
        debugPrint('Live stream error: $exception');
      },
      request: () async {
        return _apiService.joinLiveStream(liveStreamId);
      },
      onSuccess: (response) {
        value.data.isJoining = false;
        value.data.currentLiveStream =
            response['liveStream'] as LiveStreamModel;
        value.data.myParticipant =
            response['participant'] as LiveStreamParticipantModel;
        value.data.agoraToken =
            response['agoraToken'] as agora.AgoraTokenResponse;

        // Load initial data
        loadParticipants();
        loadComments();
        loadReactions();

        notifyListeners();
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  // Leave current live stream
  Future<void> leaveLiveStream() async {
    if (value.data.currentLiveStream == null) return;

    await vSafeApiCall(
      onLoading: () async {
        value.data.isLeaving = true;
        notifyListeners();
      },
      onError: (exception, trace) {
        value.data.isLeaving = false;
        notifyListeners();
        debugPrint('Live stream error: $exception');
      },
      request: () async {
        return _apiService.leaveLiveStream(value.data.currentLiveStream!.id);
      },
      onSuccess: (response) {
        value.data.isLeaving = false;
        _cleanupCurrentStream();
        notifyListeners();
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  // Load participants
  Future<void> loadParticipants({bool refresh = false}) async {
    if (value.data.currentLiveStream == null) return;

    if (refresh) {
      value.data.currentParticipantPage = 1;
      value.data.hasMoreParticipants = true;
    }

    if (value.data.isLoadingParticipants || !value.data.hasMoreParticipants) {
      return;
    }

    await vSafeApiCall(
      onLoading: () async {
        value.data.isLoadingParticipants = true;
        notifyListeners();
      },
      onError: (exception, trace) {
        value.data.isLoadingParticipants = false;
        notifyListeners();
      },
      request: () async {
        return _apiService.getParticipants(
          value.data.currentLiveStream!.id,
          page: value.data.currentParticipantPage,
          limit: 50,
        );
      },
      onSuccess: (PaginateModel<LiveStreamParticipantModel> response) {
        value.data.isLoadingParticipants = false;

        if (refresh) {
          value.data.participants = response.values;
        } else {
          value.data.participants.addAll(response.values);
        }

        value.data.hasMoreParticipants = response.hasNextPage;
        value.data.currentParticipantPage++;
        notifyListeners();
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  // Load comments
  Future<void> loadComments({bool refresh = false}) async {
    if (value.data.currentLiveStream == null) return;

    if (refresh) {
      value.data.currentCommentPage = 1;
      value.data.hasMoreComments = true;
    }

    if (value.data.isLoadingComments || !value.data.hasMoreComments) return;

    await vSafeApiCall(
      onLoading: () async {
        value.data.isLoadingComments = true;
        notifyListeners();
      },
      onError: (exception, trace) {
        value.data.isLoadingComments = false;
        notifyListeners();
      },
      request: () async {
        return _apiService.getComments(
          value.data.currentLiveStream!.id,
          page: value.data.currentCommentPage,
          limit: 50,
        );
      },
      onSuccess: (response) {
        value.data.isLoadingComments = false;

        if (refresh) {
          value.data.comments = response.values;
        } else {
          value.data.comments.addAll(response.values);
        }

        value.data.hasMoreComments = response.hasNextPage;
        value.data.currentCommentPage++;
        notifyListeners();
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  // Load reactions
  Future<void> loadReactions() async {
    if (value.data.currentLiveStream == null) return;

    await vSafeApiCall(
      onLoading: () async {
        // Don't show loading for reactions
      },
      onError: (exception, trace) {
        // Silent error for reactions
      },
      request: () async {
        return _apiService.getReactions(value.data.currentLiveStream!.id);
      },
      onSuccess: (response) {
        value.data.recentReactions =
            (response['reactions'] as PaginateModel<LiveStreamReactionModel>)
                .values;
        value.data.reactionStats = response['stats'] as LiveStreamReactionStats;
        notifyListeners();
      },
      ignoreTimeoutAndNoInternet: true,
    );
  }

  // Send comment
  Future<void> sendComment(String message) async {
    if (value.data.currentLiveStream == null || message.trim().isEmpty) return;

    await vSafeApiCall(
      onLoading: () async {
        value.data.isSendingComment = true;
        notifyListeners();
      },
      onError: (exception, trace) {
        value.data.isSendingComment = false;
        notifyListeners();
        VAppAlert.showErrorSnackBar(
          message: exception,
          context: VChatController.I.navigationContext,
        );
      },
      request: () async {
        return _apiService.addComment(AddCommentDto(
          liveStreamId: value.data.currentLiveStream!.id,
          message: message.trim(),
        ));
      },
      onSuccess: (comment) {
        value.data.isSendingComment = false;
        // Comment will be added via socket event
        notifyListeners();
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  // Send reaction
  Future<void> sendReaction(LiveStreamReactionType reactionType) async {
    if (value.data.currentLiveStream == null) return;

    await vSafeApiCall(
      onLoading: () async {
        value.data.isSendingReaction = true;
        notifyListeners();
      },
      onError: (exception, trace) {
        value.data.isSendingReaction = false;
        notifyListeners();
      },
      request: () async {
        return _apiService.addReaction(AddReactionDto(
          liveStreamId: value.data.currentLiveStream!.id,
          reactionType: reactionType,
        ));
      },
      onSuccess: (reaction) {
        value.data.isSendingReaction = false;
        // Reaction will be added via socket event
        notifyListeners();
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  // Ban user (host only)
  Future<void> banUser(String userId) async {
    if (value.data.currentLiveStream == null || !value.data.canManageStream) {
      return;
    }

    await vSafeApiCall(
      onLoading: () async {
        // Don't show loading for ban
      },
      onError: (exception, trace) {
        VAppAlert.showErrorSnackBar(
          message: exception,
          context: VChatController.I.navigationContext,
        );
      },
      request: () async {
        return _apiService.banUser(BanUserDto(
          liveStreamId: value.data.currentLiveStream!.id,
          userId: userId,
        ));
      },
      onSuccess: (response) {
        // User will be removed via socket event
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  // Pin message (host only)
  Future<void> pinMessage(String message) async {
    if (value.data.currentLiveStream == null || !value.data.canManageStream) {
      return;
    }

    await vSafeApiCall(
      onLoading: () async {
        // Don't show loading for pin
      },
      onError: (exception, trace) {
        VAppAlert.showErrorSnackBar(
          message: exception,
          context: VChatController.I.navigationContext,
        );
      },
      request: () async {
        return _apiService.pinMessage(PinMessageDto(
          liveStreamId: value.data.currentLiveStream!.id,
          message: message,
        ));
      },
      onSuccess: (pinnedMessage) {
        // Message will be pinned via socket event
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  // Get Agora token for video streaming
  Future<agora.AgoraTokenResponse> getAgoraToken(String liveStreamId) async {
    final result = await vSafeApiCall(
      onLoading: () async {
        // Don't show loading for token request
      },
      onError: (exception, trace) {
        debugPrint('Live stream error: $exception');
      },
      request: () async {
        return _apiService.getAgoraToken(liveStreamId);
      },
      onSuccess: (tokenResponse) {
        return tokenResponse;
      },
      ignoreTimeoutAndNoInternet: false,
    );

    if (result == null) {
      throw Exception('Failed to get Agora token');
    }

    return result as agora.AgoraTokenResponse;
  }

  // Error handling utilities
  void _handleError(String operation, dynamic error) {
    debugPrint('Live stream error in $operation: $error');

    // Set error state for UI feedback
    setStateError();

    // Common error recovery strategies
    switch (operation) {
      case 'loadActiveLiveStreams':
      case 'loadParticipants':
      case 'loadComments':
        // Retry after a delay for data loading operations
        Future.delayed(const Duration(seconds: 2), () {
          if (operation == 'loadActiveLiveStreams') {
            loadActiveLiveStreams(refresh: true);
          }
        });
        break;
      case 'joinLiveStream':
      case 'startLiveStream':
        // Critical operations - user should be notified
        // TODO: Show error dialog when context is available
        break;
      default:
        // For other operations, just log and continue
        break;
    }
  }

  // Validate live stream data before processing
  bool _isValidLiveStreamData(Map<String, dynamic> data, String requiredField) {
    if (data[requiredField] == null) {
      debugPrint('Missing required field: $requiredField');
      return false;
    }
    return true;
  }

  // Validate user data
  bool _isValidUserId(String? userId) {
    return userId != null && userId.isNotEmpty;
  }

  // Validate viewer count
  bool _isValidViewerCount(int? count) {
    return count != null && count >= 0;
  }

  // Edge case: Handle network connectivity issues
  void _handleNetworkError() {
    debugPrint('Network error detected, switching to offline mode');
    // TODO: Implement offline mode or retry logic
  }

  // Edge case: Handle memory pressure
  void _handleMemoryPressure() {
    debugPrint('Memory pressure detected, cleaning up old data');

    // Limit comments to 50 instead of 100
    if (value.data.comments.length > 50) {
      value.data.comments = value.data.comments.take(50).toList();
    }

    // Limit reactions to 50
    if (value.data.recentReactions.length > 50) {
      value.data.recentReactions = value.data.recentReactions.take(50).toList();
    }

    notifyListeners();
  }

  // Edge case: Handle stream interruption
  void _handleStreamInterruption() {
    debugPrint('Stream interruption detected');

    if (value.data.isInLiveStream) {
      // Try to reconnect after a delay
      Future.delayed(const Duration(seconds: 3), () {
        if (value.data.currentLiveStream != null) {
          // Attempt to rejoin the stream
          joinLiveStream(value.data.currentLiveStream!.id);
        }
      });
    }
  }
}
