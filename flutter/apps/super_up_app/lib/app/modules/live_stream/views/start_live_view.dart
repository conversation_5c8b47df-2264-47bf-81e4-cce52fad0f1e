// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';

import '../../../core/models/live_stream/live_stream_dto.dart';
import '../../../core/utils/enums.dart';
import '../controllers/live_stream_controller.dart';
import 'host_live_view.dart';

class StartLiveView extends StatefulWidget {
  const StartLiveView({super.key});

  @override
  State<StartLiveView> createState() => _StartLiveViewState();
}

class _StartLiveViewState extends State<StartLiveView> {
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _liveStreamController = GetIt.I.get<LiveStreamController>();

  LiveStreamPrivacy _selectedPrivacy = LiveStreamPrivacy.public;
  List<String> _selectedUsers = [];
  LiveStreamFiltersDto? _selectedFilters;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text('Start Live Stream'), // S.of(context).startLiveStream),
        leading: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: () => Navigator.pop(context),
          child: Text(S.of(context).cancel),
        ),
        trailing: ValueListenableBuilder<SLoadingState<LiveStreamState>>(
          valueListenable: _liveStreamController,
          builder: (context, state, child) {
            return CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: state.loadingState == VChatLoadingState.loading
                  ? null
                  : _startLiveStream,
              child: state.loadingState == VChatLoadingState.loading
                  ? const CupertinoActivityIndicator()
                  : Text(
                      'Start Streaming', // S.of(context).startStreaming,
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
            );
          },
        ),
      ),
      child: SafeArea(
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Camera Preview Section
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: CupertinoColors.black,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Stack(
                children: [
                  // Camera preview will go here
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    decoration: BoxDecoration(
                      color: CupertinoColors.systemGrey6,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Center(
                      child: Icon(
                        CupertinoIcons.video_camera,
                        size: 50,
                        color: CupertinoColors.systemGrey,
                      ),
                    ),
                  ),

                  // Filter overlay
                  if (_selectedFilters != null)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _selectedFilters!.filterType,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Title Input
            _buildSectionTitle(
                'Live Stream Title'), // S.of(context).liveStreamTitle),
            const SizedBox(height: 8),
            CupertinoTextField(
              controller: _titleController,
              placeholder:
                  'Enter stream title', // S.of(context).liveStreamTitle,
              maxLength: 100,
              decoration: BoxDecoration(
                border: Border.all(color: CupertinoColors.systemGrey4),
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.all(12),
            ),

            const SizedBox(height: 16),

            // Description Input
            _buildSectionTitle(
                'Description'), // S.of(context).liveStreamDescription),
            const SizedBox(height: 8),
            CupertinoTextField(
              controller: _descriptionController,
              placeholder:
                  'Describe your stream', // S.of(context).liveStreamDescription,
              maxLines: 3,
              maxLength: 500,
              decoration: BoxDecoration(
                border: Border.all(color: CupertinoColors.systemGrey4),
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.all(12),
            ),

            const SizedBox(height: 24),

            // Privacy Settings
            _buildSectionTitle('Who can watch'), // S.of(context).whoCanWatch),
            const SizedBox(height: 8),
            _buildPrivacySelector(),

            if (_selectedPrivacy == LiveStreamPrivacy.specificUsers) ...[
              const SizedBox(height: 16),
              _buildUserSelector(),
            ],

            const SizedBox(height: 24),

            // Filters Section
            _buildSectionTitle('Filters'), // S.of(context).applyFilters),
            const SizedBox(height: 8),
            _buildFiltersSelector(),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: context.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildPrivacySelector() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: CupertinoColors.systemGrey4),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildPrivacyOption(
            LiveStreamPrivacy.public,
            'Everyone', // S.of(context).everyone,
            CupertinoIcons.globe,
          ),
          const Divider(height: 1),
          _buildPrivacyOption(
            LiveStreamPrivacy.specificUsers,
            'Specific Friends', // S.of(context).specificFriends,
            CupertinoIcons.person_2,
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacyOption(
      LiveStreamPrivacy privacy, String title, IconData icon) {
    final isSelected = _selectedPrivacy == privacy;

    return CupertinoButton(
      padding: const EdgeInsets.all(16),
      onPressed: () {
        setState(() {
          _selectedPrivacy = privacy;
          if (privacy == LiveStreamPrivacy.public) {
            _selectedUsers.clear();
          }
        });
      },
      child: Row(
        children: [
          Icon(
            icon,
            color: isSelected
                ? CupertinoColors.systemBlue
                : CupertinoColors.systemGrey,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                color: isSelected
                    ? CupertinoColors.systemBlue
                    : CupertinoColors.label,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
          if (isSelected)
            const Icon(
              CupertinoIcons.checkmark,
              color: CupertinoColors.systemBlue,
            ),
        ],
      ),
    );
  }

  Widget _buildUserSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: CupertinoColors.systemGrey4),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          const Icon(CupertinoIcons.person_add,
              color: CupertinoColors.systemBlue),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _selectedUsers.isEmpty
                  ? 'Select Friends' // S.of(context).selectFriends
                  : '${_selectedUsers.length} friends selected',
              style: TextStyle(
                color: _selectedUsers.isEmpty
                    ? CupertinoColors.systemGrey
                    : CupertinoColors.label,
              ),
            ),
          ),
          const Icon(
            CupertinoIcons.chevron_right,
            color: CupertinoColors.systemGrey,
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSelector() {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: _showFiltersModal,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: CupertinoColors.systemGrey4),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            const Icon(CupertinoIcons.camera,
                color: CupertinoColors.systemBlue),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _selectedFilters?.filterType ??
                    'No Filters', // S.of(context).noFilters,
                style: TextStyle(
                  color: _selectedFilters != null
                      ? CupertinoColors.label
                      : CupertinoColors.systemGrey,
                ),
              ),
            ),
            const Icon(
              CupertinoIcons.chevron_right,
              color: CupertinoColors.systemGrey,
            ),
          ],
        ),
      ),
    );
  }

  void _showFiltersModal() {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => CupertinoActionSheet(
        title: Text('Apply Filters'), // S.of(context).applyFilters),
        actions: <CupertinoActionSheetAction>[
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _selectedFilters = null;
              });
            },
            child: Text('No Filters'), // S.of(context).noFilters),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _selectedFilters = const LiveStreamFiltersDto(
                  filterType: 'Beauty',
                  filterData: {'intensity': 0.5},
                );
              });
            },
            child: const Text('Beauty Filter'),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _selectedFilters = const LiveStreamFiltersDto(
                  filterType: 'Vintage',
                  filterData: {'style': 'sepia'},
                );
              });
            },
            child: const Text('Vintage Filter'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          isDefaultAction: true,
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text(S.of(context).cancel),
        ),
      ),
    );
  }

  void _startLiveStream() {
    if (_titleController.text.trim().isEmpty) {
      VAppAlert.showErrorSnackBar(
        message: 'Please enter a title for your live stream',
        context: context,
      );
      return;
    }

    if (_selectedPrivacy == LiveStreamPrivacy.specificUsers &&
        _selectedUsers.isEmpty) {
      VAppAlert.showErrorSnackBar(
        message: 'Please select friends to share with',
        context: context,
      );
      return;
    }

    final dto = CreateLiveStreamDto(
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      privacy: _selectedPrivacy,
      allowedUsers: _selectedPrivacy == LiveStreamPrivacy.specificUsers
          ? _selectedUsers
          : null,
      filters: _selectedFilters,
    );

    _liveStreamController.startLiveStream(dto).then((_) {
      if (_liveStreamController.value.data.isInLiveStream) {
        // Navigate to host live view
        Navigator.pop(context);
        context.toPage(HostLiveView(
          liveStream: _liveStreamController.value.data.currentLiveStream!,
        ));
      }
    });
  }
}
