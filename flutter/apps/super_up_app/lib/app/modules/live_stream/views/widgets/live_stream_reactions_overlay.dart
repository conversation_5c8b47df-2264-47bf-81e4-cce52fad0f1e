// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';

import '../../../../core/models/live_stream/live_stream_reaction_model.dart';
import '../../../../core/utils/enums.dart';
import '../../controllers/live_stream_controller.dart';

class LiveStreamReactionsOverlay extends StatefulWidget {
  const LiveStreamReactionsOverlay({super.key});

  @override
  State<LiveStreamReactionsOverlay> createState() =>
      _LiveStreamReactionsOverlayState();
}

class _LiveStreamReactionsOverlayState extends State<LiveStreamReactionsOverlay>
    with TickerProviderStateMixin {
  late final LiveStreamController _liveStreamController;
  final List<AnimationController> _animationControllers = [];
  final List<Widget> _floatingReactions = [];

  @override
  void initState() {
    super.initState();
    _liveStreamController = GetIt.I.get<LiveStreamController>();
    _liveStreamController.addListener(_onReactionsUpdated);
  }

  @override
  void dispose() {
    _liveStreamController.removeListener(_onReactionsUpdated);
    for (final controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _onReactionsUpdated() {
    // Add new floating reactions when reactions are updated
    final recentReactions = _liveStreamController.value.data.recentReactions;
    if (recentReactions.isNotEmpty) {
      final latestReaction = recentReactions.first;
      _addFloatingReaction(latestReaction.reactionEmoji);
    }
  }

  void _addFloatingReaction(String emoji) {
    final animationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    final animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animationController,
      curve: Curves.easeOut,
    ));

    final random = Random();
    final startX = random.nextDouble() * 100; // Random horizontal position

    final floatingReaction = AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Positioned(
          bottom: animation.value * 300, // Float upward
          right: startX,
          child: Opacity(
            opacity: 1 - animation.value,
            child: Transform.scale(
              scale: 1 + (animation.value * 0.5),
              child: Text(
                emoji,
                style: const TextStyle(fontSize: 24),
              ),
            ),
          ),
        );
      },
    );

    setState(() {
      _floatingReactions.add(floatingReaction);
      _animationControllers.add(animationController);
    });

    animationController.forward().then((_) {
      setState(() {
        _floatingReactions.remove(floatingReaction);
        _animationControllers.remove(animationController);
      });
      animationController.dispose();
    });
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<SLoadingState<LiveStreamState>>(
      valueListenable: _liveStreamController,
      builder: (context, state, child) {
        return SizedBox(
          width: 120,
          height: 300,
          child: Stack(
            children: [
              // Floating reactions
              ..._floatingReactions,

              // Reaction buttons
              Positioned(
                bottom: 0,
                right: 0,
                child: Column(
                  children: [
                    // Heart reaction
                    _buildReactionButton(
                      emoji: '❤️',
                      reactionType: LiveStreamReactionType.heart,
                      isLoading: state.data.isSendingReaction,
                    ),
                    const SizedBox(height: 8),

                    // Like reaction
                    _buildReactionButton(
                      emoji: '👍',
                      reactionType: LiveStreamReactionType.like,
                      isLoading: state.data.isSendingReaction,
                    ),
                    const SizedBox(height: 8),

                    // Laugh reaction
                    _buildReactionButton(
                      emoji: '😂',
                      reactionType: LiveStreamReactionType.laugh,
                      isLoading: state.data.isSendingReaction,
                    ),
                    const SizedBox(height: 8),

                    // Wow reaction
                    _buildReactionButton(
                      emoji: '😮',
                      reactionType: LiveStreamReactionType.wow,
                      isLoading: state.data.isSendingReaction,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildReactionButton({
    required String emoji,
    required LiveStreamReactionType reactionType,
    required bool isLoading,
  }) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: isLoading ? null : () => _sendReaction(reactionType),
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.6),
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            emoji,
            style: const TextStyle(fontSize: 24),
          ),
        ),
      ),
    );
  }

  void _sendReaction(LiveStreamReactionType reactionType) {
    _liveStreamController.sendReaction(reactionType);

    // Add local floating reaction for immediate feedback
    final emoji = _getEmojiForReactionType(reactionType);
    _addFloatingReaction(emoji);
  }

  String _getEmojiForReactionType(LiveStreamReactionType type) {
    switch (type) {
      case LiveStreamReactionType.heart:
        return '❤️';
      case LiveStreamReactionType.like:
        return '👍';
      case LiveStreamReactionType.laugh:
        return '😂';
      case LiveStreamReactionType.wow:
        return '😮';
      case LiveStreamReactionType.sad:
        return '😢';
      case LiveStreamReactionType.angry:
        return '😡';
    }
  }
}

class LiveStreamReactionStats extends StatelessWidget {
  const LiveStreamReactionStats({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<SLoadingState<LiveStreamState>>(
      valueListenable: GetIt.I.get<LiveStreamController>(),
      builder: (context, state, child) {
        final stats = state.data.reactionStats;
        if (stats == null || stats.totalReactions == 0) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.6),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              ...stats.topReactions.take(3).map((reaction) => Padding(
                    padding: const EdgeInsets.only(right: 4),
                    child: Text(
                      reaction.emoji,
                      style: const TextStyle(fontSize: 16),
                    ),
                  )),
              const SizedBox(width: 4),
              Text(
                '${stats.totalReactions}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
