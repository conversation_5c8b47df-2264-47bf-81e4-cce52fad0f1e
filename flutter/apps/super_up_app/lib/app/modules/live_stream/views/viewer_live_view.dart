// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';

import '../../../core/models/live_stream/live_stream_model.dart';
import '../controllers/live_stream_controller.dart';
import 'widgets/live_stream_video_view.dart';
import 'widgets/live_stream_comments_overlay.dart';
import 'widgets/live_stream_reactions_overlay.dart';
import 'widgets/live_stream_viewer_controls.dart';

class ViewerLiveView extends StatefulWidget {
  final LiveStreamModel liveStream;

  const ViewerLiveView({
    super.key,
    required this.liveStream,
  });

  @override
  State<ViewerLiveView> createState() => _ViewerLiveViewState();
}

class _ViewerLiveViewState extends State<ViewerLiveView> {
  late final LiveStreamController _liveStreamController;
  bool _showControls = true;
  bool _showComments = true;

  @override
  void initState() {
    super.initState();
    _liveStreamController = GetIt.I.get<LiveStreamController>();
    _startAutoHideControls();
    
    // Join the live stream
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _joinLiveStream();
    });
  }

  void _startAutoHideControls() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    
    if (_showControls) {
      _startAutoHideControls();
    }
  }

  void _joinLiveStream() {
    _liveStreamController.joinLiveStream(widget.liveStream.id);
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: Colors.black,
      child: SafeArea(
        child: ValueListenableBuilder<SLoadingState<LiveStreamState>>(
          valueListenable: _liveStreamController,
          builder: (context, state, child) {
            return Stack(
              children: [
                // Video Stream Background
                const LiveStreamVideoView(isHost: false),
                
                // Tap to toggle controls
                GestureDetector(
                  onTap: _toggleControls,
                  child: Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: Colors.transparent,
                  ),
                ),
                
                // Top Info Bar (always visible)
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withOpacity(0.6),
                          Colors.transparent,
                        ],
                      ),
                    ),
                    child: Row(
                      children: [
                        // Host avatar
                        VCircleAvatar(
                          fullName: widget.liveStream.hostData?.fullName ?? 'Host',
                          imageUrl: widget.liveStream.hostData?.userImage,
                          radius: 16,
                        ),
                        
                        const SizedBox(width: 12),
                        
                        // Host info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                widget.liveStream.hostData?.fullName ?? 'Unknown Host',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                ),
                              ),
                              Text(
                                widget.liveStream.title,
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.8),
                                  fontSize: 12,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                        
                        // Live indicator
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                width: 6,
                                height: 6,
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 4),
                              const Text(
                                'LIVE',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 10,
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(width: 12),
                        
                        // Close button
                        CupertinoButton(
                          padding: EdgeInsets.zero,
                          onPressed: _leaveLiveStream,
                          child: Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              CupertinoIcons.xmark,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                // Stream Stats
                Positioned(
                  top: 80,
                  left: 16,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          CupertinoIcons.eye,
                          color: Colors.white,
                          size: 12,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${state.data.viewerCount}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Icon(
                          CupertinoIcons.clock,
                          color: Colors.white,
                          size: 12,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          state.data.currentLiveStream?.durationText ?? '00:00',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                // Pinned Message
                if (state.data.currentLiveStream?.hasPinnedMessage == true)
                  Positioned(
                    top: 120,
                    left: 16,
                    right: 16,
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.amber.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            CupertinoIcons.pin,
                            color: Colors.black,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              state.data.currentLiveStream!.pinnedMessage!.message,
                              style: const TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                
                // Comments Overlay
                if (_showComments)
                  const Positioned(
                    bottom: 120,
                    left: 16,
                    right: 80,
                    child: LiveStreamCommentsOverlay(),
                  ),
                
                // Reactions Overlay
                const Positioned(
                  bottom: 200,
                  right: 16,
                  child: LiveStreamReactionsOverlay(),
                ),
                
                // Viewer Controls
                if (_showControls)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: LiveStreamViewerControls(
                      onToggleComments: () {
                        setState(() {
                          _showComments = !_showComments;
                        });
                      },
                      showComments: _showComments,
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _leaveLiveStream() {
    _liveStreamController.leaveLiveStream().then((_) {
      Navigator.pop(context);
      VAppAlert.showInfoSnackBar(
        message: S.of(context).leaveLiveStream,
        context: context,
      );
    });
  }

  @override
  void dispose() {
    // Leave the stream when disposing
    _liveStreamController.leaveLiveStream();
    super.dispose();
  }
}
