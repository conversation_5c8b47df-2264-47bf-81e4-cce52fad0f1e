// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';

import '../../controllers/live_stream_controller.dart';

class LiveStreamCommentInput extends StatefulWidget {
  const LiveStreamCommentInput({super.key});

  @override
  State<LiveStreamCommentInput> createState() => _LiveStreamCommentInputState();
}

class _LiveStreamCommentInputState extends State<LiveStreamCommentInput> {
  final _commentController = TextEditingController();
  late final LiveStreamController _liveStreamController;

  @override
  void initState() {
    super.initState();
    _liveStreamController = GetIt.I.get<LiveStreamController>();
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<SLoadingState<LiveStreamState>>(
      valueListenable: _liveStreamController,
      builder: (context, state, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.8),
            border: Border(
              top: BorderSide(
                color: Colors.white.withOpacity(0.1),
                width: 0.5,
              ),
            ),
          ),
          child: Row(
            children: [
              // Comment input
              Expanded(
                child: CupertinoTextField(
                  controller: _commentController,
                  placeholder:
                      'Add a comment...', // S.of(context).commentPlaceholder,
                  placeholderStyle: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                  ),
                  style: const TextStyle(
                    color: Colors.white,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 0.5,
                    ),
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  maxLines: 1,
                  onSubmitted: _sendComment,
                ),
              ),

              const SizedBox(width: 12),

              // Send button
              CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: state.data.isSendingComment
                    ? null
                    : () => _sendComment(_commentController.text),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemBlue,
                    shape: BoxShape.circle,
                  ),
                  child: state.data.isSendingComment
                      ? const CupertinoActivityIndicator(
                          color: Colors.white,
                        )
                      : const Icon(
                          CupertinoIcons.paperplane,
                          color: Colors.white,
                          size: 18,
                        ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _sendComment(String message) {
    if (message.trim().isEmpty) return;

    _liveStreamController.sendComment(message.trim()).then((_) {
      _commentController.clear();
    });
  }
}
