// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';

import '../../controllers/live_stream_controller.dart';
import 'live_stream_comment_input.dart';

class LiveStreamViewerControls extends StatefulWidget {
  final VoidCallback onToggleComments;
  final bool showComments;

  const LiveStreamViewerControls({
    super.key,
    required this.onToggleComments,
    required this.showComments,
  });

  @override
  State<LiveStreamViewerControls> createState() =>
      _LiveStreamViewerControlsState();
}

class _LiveStreamViewerControlsState extends State<LiveStreamViewerControls> {
  late final LiveStreamController _liveStreamController;
  bool _showCommentInput = false;

  @override
  void initState() {
    super.initState();
    _liveStreamController = GetIt.I.get<LiveStreamController>();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<SLoadingState<LiveStreamState>>(
      valueListenable: _liveStreamController,
      builder: (context, state, child) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Comment input (when active)
            if (_showCommentInput) const LiveStreamCommentInput(),

            // Main controls
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.8),
                  ],
                ),
              ),
              child: Row(
                children: [
                  // Comment button
                  _buildControlButton(
                    icon: widget.showComments
                        ? CupertinoIcons.chat_bubble_fill
                        : CupertinoIcons.chat_bubble,
                    onTap: widget.onToggleComments,
                    isActive: widget.showComments,
                  ),

                  const SizedBox(width: 12),

                  // Add comment button
                  _buildControlButton(
                    icon: CupertinoIcons.plus_bubble,
                    onTap: _toggleCommentInput,
                    isActive: _showCommentInput,
                  ),

                  const SizedBox(width: 12),

                  // Share button
                  _buildControlButton(
                    icon: CupertinoIcons.share,
                    onTap: _shareStream,
                  ),

                  const SizedBox(width: 12),

                  // Report button
                  _buildControlButton(
                    icon: CupertinoIcons.flag,
                    onTap: _reportStream,
                  ),

                  const Spacer(),

                  // Participants count
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          CupertinoIcons.person_2,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          '${state.data.participants.length}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onTap,
    bool isActive = false,
  }) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onTap,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: isActive
              ? CupertinoColors.systemBlue.withOpacity(0.8)
              : Colors.white.withOpacity(0.2),
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  void _toggleCommentInput() {
    setState(() {
      _showCommentInput = !_showCommentInput;
    });
  }

  void _shareStream() {
    // TODO: Implement stream sharing
    VAppAlert.showSuccessSnackBar(
      message: 'Share stream feature coming soon',
      context: context,
    );
  }

  void _reportStream() {
    showCupertinoDialog<void>(
      context: context,
      builder: (BuildContext context) => CupertinoAlertDialog(
        title: Text(S.of(context).report),
        content:
            const Text('Report this live stream for inappropriate content?'),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            child: Text(S.of(context).cancel),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () {
              Navigator.pop(context);
              _submitReport();
            },
            child: Text(S.of(context).report),
          ),
        ],
      ),
    );
  }

  void _submitReport() {
    // TODO: Implement report submission
    VAppAlert.showSuccessSnackBar(
      message: 'Report submitted successfully',
      context: context,
    );
  }
}
