// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:super_up_core/super_up_core.dart';

import '../../../../core/models/live_stream/live_stream_comment_model.dart';
import '../../controllers/live_stream_controller.dart';

class LiveStreamCommentsOverlay extends StatefulWidget {
  const LiveStreamCommentsOverlay({super.key});

  @override
  State<LiveStreamCommentsOverlay> createState() => _LiveStreamCommentsOverlayState();
}

class _LiveStreamCommentsOverlayState extends State<LiveStreamCommentsOverlay>
    with TickerProviderStateMixin {
  late final LiveStreamController _liveStreamController;
  late final AnimationController _animationController;
  late final Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _liveStreamController = Get.find<LiveStreamController>();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<SLoadingState<LiveStreamState>>(
      valueListenable: _liveStreamController,
      builder: (context, state, child) {
        final comments = state.data.comments.take(5).toList(); // Show last 5 comments
        
        return SlideTransition(
          position: _slideAnimation,
          child: Container(
            constraints: const BoxConstraints(maxHeight: 200),
            child: ListView.builder(
              reverse: true,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: comments.length,
              itemBuilder: (context, index) {
                final comment = comments[index];
                return _buildCommentBubble(comment);
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildCommentBubble(LiveStreamCommentModel comment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: TweenAnimationBuilder<double>(
        duration: const Duration(milliseconds: 500),
        tween: Tween(begin: 0.0, end: 1.0),
        builder: (context, value, child) {
          return Transform.scale(
            scale: value,
            alignment: Alignment.bottomLeft,
            child: Opacity(
              opacity: value,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 0.5,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // User name
                    Text(
                      comment.userData?.fullName ?? 'Unknown User',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    
                    // Comment message
                    Text(
                      comment.displayMessage,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

class LiveStreamCommentInput extends StatefulWidget {
  const LiveStreamCommentInput({super.key});

  @override
  State<LiveStreamCommentInput> createState() => _LiveStreamCommentInputState();
}

class _LiveStreamCommentInputState extends State<LiveStreamCommentInput> {
  final _commentController = TextEditingController();
  late final LiveStreamController _liveStreamController;

  @override
  void initState() {
    super.initState();
    _liveStreamController = Get.find<LiveStreamController>();
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<SLoadingState<LiveStreamState>>(
      valueListenable: _liveStreamController,
      builder: (context, state, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.8),
            border: Border(
              top: BorderSide(
                color: Colors.white.withOpacity(0.1),
                width: 0.5,
              ),
            ),
          ),
          child: Row(
            children: [
              // Comment input
              Expanded(
                child: CupertinoTextField(
                  controller: _commentController,
                  placeholder: 'Add a comment...',
                  placeholderStyle: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                  ),
                  style: const TextStyle(
                    color: Colors.white,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 0.5,
                    ),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  maxLines: 1,
                  onSubmitted: _sendComment,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Send button
              CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: state.data.isSendingComment ? null : () => _sendComment(_commentController.text),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemBlue,
                    shape: BoxShape.circle,
                  ),
                  child: state.data.isSendingComment
                      ? const CupertinoActivityIndicator(
                          color: Colors.white,
                        )
                      : const Icon(
                          CupertinoIcons.paperplane,
                          color: Colors.white,
                          size: 18,
                        ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _sendComment(String message) {
    if (message.trim().isEmpty) return;
    
    _liveStreamController.sendComment(message.trim()).then((_) {
      _commentController.clear();
    });
  }
}
