// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';

import '../../../../core/models/live_stream/live_stream_participant_model.dart';
import '../../../../core/utils/enums.dart';
import '../../controllers/live_stream_controller.dart';

class LiveStreamParticipantsView extends StatefulWidget {
  final bool isHost;

  const LiveStreamParticipantsView({
    super.key,
    this.isHost = false,
  });

  @override
  State<LiveStreamParticipantsView> createState() => _LiveStreamParticipantsViewState();
}

class _LiveStreamParticipantsViewState extends State<LiveStreamParticipantsView> {
  late final LiveStreamController _liveStreamController;

  @override
  void initState() {
    super.initState();
    _liveStreamController = GetIt.I.get<LiveStreamController>();
    _loadParticipants();
  }

  void _loadParticipants() {
    _liveStreamController.loadParticipants(refresh: true);
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text(S.of(context).participants),
        leading: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: () => Navigator.pop(context),
          child: Text(S.of(context).cancel),
        ),
        trailing: widget.isHost
            ? CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: _showInviteUsers,
                child: const Icon(CupertinoIcons.person_add),
              )
            : null,
      ),
      child: SafeArea(
        child: ValueListenableBuilder<SLoadingState<LiveStreamState>>(
          valueListenable: _liveStreamController,
          builder: (context, state, child) {
            return VAsyncWidgetsBuilder(
              loadingState: state.data.isLoadingParticipants 
                  ? SLoadingState.loading() 
                  : SLoadingState.success(),
              onRefresh: _loadParticipants,
              successWidget: () {
                final participants = state.data.participants;
                
                if (participants.isEmpty) {
                  return const Center(
                    child: Text('No participants yet'),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: participants.length,
                  itemBuilder: (context, index) {
                    final participant = participants[index];
                    return _buildParticipantTile(participant);
                  },
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildParticipantTile(LiveStreamParticipantModel participant) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          // Avatar
          VCircleAvatar(
            fullName: participant.userData?.fullName ?? 'User',
            imageUrl: participant.userData?.userImage,
            radius: 24,
          ),
          
          const SizedBox(width: 16),
          
          // User info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      participant.userData?.fullName ?? 'Unknown User',
                      style: context.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (participant.role == LiveStreamParticipantRole.host) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: CupertinoColors.systemBlue,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          'HOST',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      CupertinoIcons.clock,
                      size: 12,
                      color: CupertinoColors.systemGrey,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Joined ${participant.joinTimeText}',
                      style: context.textTheme.bodySmall?.copyWith(
                        color: CupertinoColors.systemGrey,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Icon(
                      CupertinoIcons.time,
                      size: 12,
                      color: CupertinoColors.systemGrey,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Watched ${participant.watchDurationText}',
                      style: context.textTheme.bodySmall?.copyWith(
                        color: CupertinoColors.systemGrey,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Status indicators
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (participant.isMuted)
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    CupertinoIcons.mic_slash,
                    size: 16,
                    color: Colors.red,
                  ),
                ),
              
              if (widget.isHost && participant.role != LiveStreamParticipantRole.host) ...[
                const SizedBox(width: 8),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () => _showParticipantActions(participant),
                  child: const Icon(
                    CupertinoIcons.ellipsis,
                    color: CupertinoColors.systemGrey,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  void _showParticipantActions(LiveStreamParticipantModel participant) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => CupertinoActionSheet(
        title: Text(participant.userData?.fullName ?? 'User Actions'),
        actions: <CupertinoActionSheetAction>[
          if (!participant.isMuted)
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context);
                _muteParticipant(participant);
              },
              child: Text(S.of(context).mute),
            )
          else
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context);
                _unmuteParticipant(participant);
              },
              child: Text(S.of(context).unMute),
            ),
          CupertinoActionSheetAction(
            isDestructiveAction: true,
            onPressed: () {
              Navigator.pop(context);
              _banParticipant(participant);
            },
            child: Text(S.of(context).banUser),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          isDefaultAction: true,
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text(S.of(context).cancel),
        ),
      ),
    );
  }

  void _muteParticipant(LiveStreamParticipantModel participant) {
    // TODO: Implement mute participant
    VAppAlert.showInfoSnackBar(
      message: 'Muted ${participant.userData?.fullName}',
      context: context,
    );
  }

  void _unmuteParticipant(LiveStreamParticipantModel participant) {
    // TODO: Implement unmute participant
    VAppAlert.showInfoSnackBar(
      message: 'Unmuted ${participant.userData?.fullName}',
      context: context,
    );
  }

  void _banParticipant(LiveStreamParticipantModel participant) {
    showCupertinoDialog<void>(
      context: context,
      builder: (BuildContext context) => CupertinoAlertDialog(
        title: Text(S.of(context).banUser),
        content: Text('Ban ${participant.userData?.fullName} from this live stream?'),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            child: Text(S.of(context).cancel),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () {
              Navigator.pop(context);
              _confirmBanParticipant(participant);
            },
            child: Text(S.of(context).banUser),
          ),
        ],
      ),
    );
  }

  void _confirmBanParticipant(LiveStreamParticipantModel participant) {
    _liveStreamController.banUser(participant.userId).then((_) {
      VAppAlert.showSuccessSnackBar(
        message: 'Banned ${participant.userData?.fullName}',
        context: context,
      );
    });
  }

  void _showInviteUsers() {
    // TODO: Implement invite users
    VAppAlert.showInfoSnackBar(
      message: 'Invite users feature coming soon',
      context: context,
    );
  }
}
