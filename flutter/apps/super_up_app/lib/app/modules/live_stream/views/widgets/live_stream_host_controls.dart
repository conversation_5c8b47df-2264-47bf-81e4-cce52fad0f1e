// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';

import '../../controllers/live_stream_controller.dart';
import 'live_stream_comment_input.dart';
import 'live_stream_participants_view.dart';
import 'live_stream_settings_view.dart';

class LiveStreamHostControls extends StatefulWidget {
  final VoidCallback onToggleComments;
  final bool showComments;

  const LiveStreamHostControls({
    super.key,
    required this.onToggleComments,
    required this.showComments,
  });

  @override
  State<LiveStreamHostControls> createState() => _LiveStreamHostControlsState();
}

class _LiveStreamHostControlsState extends State<LiveStreamHostControls> {
  late final LiveStreamController _liveStreamController;
  bool _showCommentInput = false;

  @override
  void initState() {
    super.initState();
    _liveStreamController = GetIt.I.get<LiveStreamController>();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<SLoadingState<LiveStreamState>>(
      valueListenable: _liveStreamController,
      builder: (context, state, child) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Comment input (when active)
            if (_showCommentInput) const LiveStreamCommentInput(),

            // Main controls
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.8),
                  ],
                ),
              ),
              child: Row(
                children: [
                  // Comment button
                  _buildControlButton(
                    icon: widget.showComments
                        ? CupertinoIcons.chat_bubble_fill
                        : CupertinoIcons.chat_bubble,
                    onTap: widget.onToggleComments,
                    isActive: widget.showComments,
                  ),

                  const SizedBox(width: 12),

                  // Add comment button
                  _buildControlButton(
                    icon: CupertinoIcons.plus_bubble,
                    onTap: _toggleCommentInput,
                    isActive: _showCommentInput,
                  ),

                  const SizedBox(width: 12),

                  // Participants button
                  _buildControlButton(
                    icon: CupertinoIcons.person_2,
                    onTap: _showParticipants,
                    badge: state.data.participants.length.toString(),
                  ),

                  const SizedBox(width: 12),

                  // Host controls button
                  _buildControlButton(
                    icon: CupertinoIcons.settings,
                    onTap: _showHostControls,
                  ),

                  const Spacer(),

                  // Pin message button
                  _buildControlButton(
                    icon: CupertinoIcons.pin,
                    onTap: _showPinMessageDialog,
                  ),

                  const SizedBox(width: 12),

                  // Invite users button
                  _buildControlButton(
                    icon: CupertinoIcons.person_add,
                    onTap: _showInviteUsers,
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onTap,
    bool isActive = false,
    String? badge,
  }) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onTap,
      child: Stack(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: isActive
                  ? CupertinoColors.systemBlue.withOpacity(0.8)
                  : Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),

          // Badge
          if (badge != null)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                constraints: const BoxConstraints(
                  minWidth: 20,
                  minHeight: 20,
                ),
                child: Text(
                  badge,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _toggleCommentInput() {
    setState(() {
      _showCommentInput = !_showCommentInput;
    });
  }

  void _showParticipants() {
    context.toPage(const LiveStreamParticipantsView(isHost: true));
  }

  void _showHostControls() {
    context.toPage(const LiveStreamSettingsView(isHost: true));
  }

  void _showPinMessageDialog() {
    final messageController = TextEditingController();

    showCupertinoDialog<void>(
      context: context,
      builder: (BuildContext context) => CupertinoAlertDialog(
        title: Text(S.of(context).pinMessage),
        content: Column(
          children: [
            const SizedBox(height: 16),
            CupertinoTextField(
              controller: messageController,
              placeholder: 'Enter message to pin...',
              maxLines: 2,
            ),
          ],
        ),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            child: Text(S.of(context).cancel),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          CupertinoDialogAction(
            isDefaultAction: true,
            onPressed: () {
              Navigator.pop(context);
              if (messageController.text.trim().isNotEmpty) {
                _liveStreamController.pinMessage(messageController.text.trim());
              }
            },
            child: Text(S.of(context).pinMessage),
          ),
        ],
      ),
    );
  }

  void _showInviteUsers() {
    // TODO: Implement user invitation
    VAppAlert.showInfoSnackBar(
      message: 'Invite users feature coming soon',
      context: context,
    );
  }

  void _showBanUserDialog() {
    // TODO: Implement ban user dialog
    VAppAlert.showInfoSnackBar(
      message: 'Ban user feature coming soon',
      context: context,
    );
  }

  void _showUnbanUserDialog() {
    // TODO: Implement unban user dialog
    VAppAlert.showInfoSnackBar(
      message: 'Unban user feature coming soon',
      context: context,
    );
  }
}
