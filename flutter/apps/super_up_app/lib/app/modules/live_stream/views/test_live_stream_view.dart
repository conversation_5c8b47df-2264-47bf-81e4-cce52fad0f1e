// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up_core/super_up_core.dart';

import '../test/live_stream_api_test.dart';

class TestLiveStreamView extends StatefulWidget {
  const TestLiveStreamView({super.key});

  @override
  State<TestLiveStreamView> createState() => _TestLiveStreamViewState();
}

class _TestLiveStreamViewState extends State<TestLiveStreamView> {
  final List<String> _testResults = [];
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('Live Stream API Test'),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Test Controls
              Row(
                children: [
                  Expanded(
                    child: CupertinoButton.filled(
                      onPressed: _isRunning ? null : _runAllTests,
                      child: _isRunning
                          ? const CupertinoActivityIndicator(color: Colors.white)
                          : const Text('Run All Tests'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: CupertinoButton(
                      onPressed: _clearResults,
                      child: const Text('Clear Results'),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Individual Test Buttons
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildTestButton('Get Active Streams', () => _runSingleTest('getactivelivestreams')),
                  _buildTestButton('Create Stream', () => _runSingleTest('createlivestream')),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // Results Section
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemGrey6,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Test Results',
                        style: context.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Expanded(
                        child: _testResults.isEmpty
                            ? const Center(
                                child: Text(
                                  'No tests run yet.\nTap "Run All Tests" to start.',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: CupertinoColors.systemGrey,
                                  ),
                                ),
                              )
                            : ListView.builder(
                                itemCount: _testResults.length,
                                itemBuilder: (context, index) {
                                  final result = _testResults[index];
                                  final isSuccess = result.contains('✅');
                                  final isError = result.contains('❌');
                                  
                                  return Container(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: isSuccess
                                          ? Colors.green.withOpacity(0.1)
                                          : isError
                                              ? Colors.red.withOpacity(0.1)
                                              : Colors.blue.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: isSuccess
                                            ? Colors.green.withOpacity(0.3)
                                            : isError
                                                ? Colors.red.withOpacity(0.3)
                                                : Colors.blue.withOpacity(0.3),
                                      ),
                                    ),
                                    child: Text(
                                      result,
                                      style: TextStyle(
                                        fontFamily: 'monospace',
                                        fontSize: 12,
                                        color: isSuccess
                                            ? Colors.green.shade700
                                            : isError
                                                ? Colors.red.shade700
                                                : Colors.blue.shade700,
                                      ),
                                    ),
                                  );
                                },
                              ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTestButton(String title, VoidCallback onPressed) {
    return CupertinoButton(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: CupertinoColors.systemBlue.withOpacity(0.1),
      onPressed: _isRunning ? null : onPressed,
      child: Text(
        title,
        style: const TextStyle(
          color: CupertinoColors.systemBlue,
          fontSize: 12,
        ),
      ),
    );
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isRunning = true;
      _testResults.clear();
    });

    // Capture logs
    final originalPrint = print;
    print = (Object? object) {
      if (mounted) {
        setState(() {
          _testResults.add(object.toString());
        });
      }
      originalPrint(object);
    };

    try {
      await LiveStreamApiTest.runAllTests();
    } catch (e) {
      if (mounted) {
        setState(() {
          _testResults.add('❌ Test suite failed: $e');
        });
      }
    } finally {
      print = originalPrint;
      if (mounted) {
        setState(() {
          _isRunning = false;
        });
      }
    }
  }

  Future<void> _runSingleTest(String testName) async {
    setState(() {
      _isRunning = true;
    });

    // Capture logs
    final originalPrint = print;
    print = (Object? object) {
      if (mounted) {
        setState(() {
          _testResults.add(object.toString());
        });
      }
      originalPrint(object);
    };

    try {
      await LiveStreamApiTest.testEndpoint(testName);
    } catch (e) {
      if (mounted) {
        setState(() {
          _testResults.add('❌ Test failed: $e');
        });
      }
    } finally {
      print = originalPrint;
      if (mounted) {
        setState(() {
          _isRunning = false;
        });
      }
    }
  }

  void _clearResults() {
    setState(() {
      _testResults.clear();
    });
  }
}
