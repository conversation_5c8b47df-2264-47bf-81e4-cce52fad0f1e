// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';

import '../../controllers/live_stream_controller.dart';
import 'live_stream_participants_view.dart';

class LiveStreamSettingsView extends StatefulWidget {
  final bool isHost;

  const LiveStreamSettingsView({
    super.key,
    this.isHost = false,
  });

  @override
  State<LiveStreamSettingsView> createState() => _LiveStreamSettingsViewState();
}

class _LiveStreamSettingsViewState extends State<LiveStreamSettingsView> {
  late final LiveStreamController _liveStreamController;

  @override
  void initState() {
    super.initState();
    _liveStreamController = GetIt.I.get<LiveStreamController>();
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text(widget.isHost
            ? 'Host Controls'
            : 'Live Stream'), // S.of(context).hostControls
        leading: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: () => Navigator.pop(context),
          child: Text(S.of(context).cancel),
        ),
      ),
      child: SafeArea(
        child: ValueListenableBuilder<SLoadingState<LiveStreamState>>(
          valueListenable: _liveStreamController,
          builder: (context, state, child) {
            return ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // Stream Info Section
                _buildSection(
                  title: 'Stream Information',
                  children: [
                    _buildInfoTile(
                      icon: CupertinoIcons.tv,
                      title:
                          state.data.currentLiveStream?.title ?? 'Live Stream',
                      subtitle: state.data.currentLiveStream?.description,
                    ),
                    _buildInfoTile(
                      icon: CupertinoIcons.eye,
                      title: '${state.data.viewerCount} Viewers',
                      subtitle: 'Currently watching',
                    ),
                    _buildInfoTile(
                      icon: CupertinoIcons.clock,
                      title:
                          state.data.currentLiveStream?.durationText ?? '00:00',
                      subtitle: 'Stream duration',
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Participants Section
                _buildSection(
                  title: 'Participants', // S.of(context).participants,
                  children: [
                    _buildActionTile(
                      icon: CupertinoIcons.person_2,
                      title: 'Participants', // S.of(context).participants,
                      subtitle:
                          '${state.data.participants.length} participants',
                      onTap: _showParticipants,
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Host Controls (only for host)
                if (widget.isHost) ...[
                  _buildSection(
                    title: 'Host Controls', // S.of(context).hostControls,
                    children: [
                      _buildActionTile(
                        icon: CupertinoIcons.person_add,
                        title: 'Invite Users', // S.of(context).inviteUsers,
                        subtitle: 'Invite friends to join',
                        onTap: _inviteUsers,
                      ),
                      _buildActionTile(
                        icon: CupertinoIcons.pin,
                        title: 'Pin Message', // S.of(context).pinMessage,
                        subtitle: 'Pin a message for all viewers',
                        onTap: _pinMessage,
                      ),
                      _buildActionTile(
                        icon: CupertinoIcons.nosign,
                        title: 'Banned Users',
                        subtitle: 'Manage banned users',
                        onTap: _showBannedUsers,
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                ],

                // Actions Section
                _buildSection(
                  title: 'Actions',
                  children: [
                    if (!widget.isHost)
                      _buildActionTile(
                        icon: CupertinoIcons.share,
                        title: S.of(context).share,
                        subtitle: 'Share this live stream',
                        onTap: _shareStream,
                      ),
                    if (!widget.isHost)
                      _buildActionTile(
                        icon: CupertinoIcons.flag,
                        title: S.of(context).report,
                        subtitle: 'Report inappropriate content',
                        onTap: _reportStream,
                        isDestructive: true,
                      ),
                    _buildActionTile(
                      icon: widget.isHost
                          ? CupertinoIcons.stop
                          : CupertinoIcons.xmark,
                      title: widget.isHost
                          ? 'End Live Stream' // S.of(context).endLiveStream
                          : 'Leave Live Stream', // S.of(context).leaveLiveStream,
                      subtitle: widget.isHost
                          ? 'End stream for everyone'
                          : 'Leave this live stream',
                      onTap: widget.isHost ? _endStream : _leaveStream,
                      isDestructive: true,
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: context.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: CupertinoColors.systemGrey6,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoTile({
    required IconData icon,
    required String title,
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(
            icon,
            color: CupertinoColors.systemBlue,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: context.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: context.textTheme.bodySmall?.copyWith(
                      color: CupertinoColors.systemGrey,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              icon,
              color: isDestructive ? Colors.red : CupertinoColors.systemBlue,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: context.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isDestructive ? Colors.red : null,
                    ),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: context.textTheme.bodySmall?.copyWith(
                        color: CupertinoColors.systemGrey,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Icon(
              CupertinoIcons.chevron_right,
              color: CupertinoColors.systemGrey,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _showParticipants() {
    context.toPage(LiveStreamParticipantsView(
      isHost: widget.isHost,
    ));
  }

  void _inviteUsers() {
    VAppAlert.showSuccessSnackBar(
      message: 'Invite users feature coming soon',
      context: context,
    );
  }

  void _pinMessage() {
    final messageController = TextEditingController();

    showCupertinoDialog<void>(
      context: context,
      builder: (BuildContext context) => CupertinoAlertDialog(
        title: Text('Pin Message'), // S.of(context).pinMessage),
        content: Column(
          children: [
            const SizedBox(height: 16),
            CupertinoTextField(
              controller: messageController,
              placeholder: 'Enter message to pin...',
              maxLines: 2,
            ),
          ],
        ),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            child: Text(S.of(context).cancel),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          CupertinoDialogAction(
            isDefaultAction: true,
            onPressed: () {
              Navigator.pop(context);
              if (messageController.text.trim().isNotEmpty) {
                _liveStreamController.pinMessage(messageController.text.trim());
              }
            },
            child: Text('Pin Message'), // S.of(context).pinMessage),
          ),
        ],
      ),
    );
  }

  void _showBannedUsers() {
    VAppAlert.showSuccessSnackBar(
      message: 'Banned users management coming soon',
      context: context,
    );
  }

  void _shareStream() {
    VAppAlert.showSuccessSnackBar(
      message: 'Share stream feature coming soon',
      context: context,
    );
  }

  void _reportStream() {
    showCupertinoDialog<void>(
      context: context,
      builder: (BuildContext context) => CupertinoAlertDialog(
        title: Text(S.of(context).report),
        content:
            const Text('Report this live stream for inappropriate content?'),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            child: Text(S.of(context).cancel),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () {
              Navigator.pop(context);
              VAppAlert.showSuccessSnackBar(
                message: 'Report submitted successfully',
                context: context,
              );
            },
            child: Text(S.of(context).report),
          ),
        ],
      ),
    );
  }

  void _endStream() {
    showCupertinoDialog<void>(
      context: context,
      builder: (BuildContext context) => CupertinoAlertDialog(
        title: Text('End Live Stream'), // S.of(context).endLiveStream),
        content: const Text('Are you sure you want to end this live stream?'),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            child: Text(S.of(context).cancel),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () {
              Navigator.pop(context);
              _liveStreamController.endLiveStream().then((_) {
                Navigator.pop(context); // Close settings
                Navigator.pop(context); // Close live stream
              });
            },
            child: Text('End Stream'), // S.of(context).endLiveStream),
          ),
        ],
      ),
    );
  }

  void _leaveStream() {
    _liveStreamController.leaveLiveStream().then((_) {
      Navigator.pop(context); // Close settings
      Navigator.pop(context); // Close live stream
    });
  }
}
