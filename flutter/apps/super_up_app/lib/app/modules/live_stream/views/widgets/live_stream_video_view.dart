// Copyright 2023, the <PERSON><PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:super_up_core/super_up_core.dart';

import '../../controllers/live_stream_controller.dart';

class LiveStreamVideoView extends StatefulWidget {
  final bool isHost;

  const LiveStreamVideoView({
    super.key,
    this.isHost = true,
  });

  @override
  State<LiveStreamVideoView> createState() => _LiveStreamVideoViewState();
}

class _LiveStreamVideoViewState extends State<LiveStreamVideoView> {
  late final LiveStreamController _liveStreamController;

  @override
  void initState() {
    super.initState();
    _liveStreamController = Get.find<LiveStreamController>();
    _initializeVideo();
  }

  void _initializeVideo() {
    // TODO: Initialize Agora video streaming
    // This will be implemented with Agora SDK integration
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<SLoadingState<LiveStreamState>>(
      valueListenable: _liveStreamController,
      builder: (context, state, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF1a1a1a),
                Color(0xFF2d2d2d),
                Color(0xFF1a1a1a),
              ],
            ),
          ),
          child: Stack(
            children: [
              // Video stream placeholder
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        CupertinoIcons.video_camera,
                        size: 60,
                        color: Colors.white54,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      widget.isHost ? 'Your Camera' : 'Live Stream',
                      style: const TextStyle(
                        color: Colors.white54,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.isHost 
                          ? 'Camera will appear here when streaming starts'
                          : 'Connecting to live stream...',
                      style: const TextStyle(
                        color: Colors.white38,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              
              // Video controls overlay (for host)
              if (widget.isHost)
                Positioned(
                  bottom: 20,
                  right: 20,
                  child: Column(
                    children: [
                      // Camera flip button
                      _buildVideoControl(
                        icon: CupertinoIcons.camera_rotate,
                        onTap: _flipCamera,
                      ),
                      const SizedBox(height: 12),
                      
                      // Camera on/off button
                      _buildVideoControl(
                        icon: CupertinoIcons.video_camera,
                        onTap: _toggleCamera,
                      ),
                      const SizedBox(height: 12),
                      
                      // Microphone on/off button
                      _buildVideoControl(
                        icon: CupertinoIcons.mic,
                        onTap: _toggleMicrophone,
                      ),
                    ],
                  ),
                ),
              
              // Connection status indicator
              if (state.data.isInLiveStream)
                Positioned(
                  top: 20,
                  right: 20,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          CupertinoIcons.wifi,
                          color: Colors.white,
                          size: 12,
                        ),
                        SizedBox(width: 4),
                        Text(
                          'Connected',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildVideoControl({
    required IconData icon,
    required VoidCallback onTap,
    bool isActive = true,
  }) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onTap,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: isActive 
              ? Colors.white.withOpacity(0.2)
              : Colors.red.withOpacity(0.8),
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  void _flipCamera() {
    // TODO: Implement camera flip
    VAppAlert.showInfoSnackBar(
      message: 'Camera flipped',
      context: context,
    );
  }

  void _toggleCamera() {
    // TODO: Implement camera toggle
    VAppAlert.showInfoSnackBar(
      message: 'Camera toggled',
      context: context,
    );
  }

  void _toggleMicrophone() {
    // TODO: Implement microphone toggle
    VAppAlert.showInfoSnackBar(
      message: 'Microphone toggled',
      context: context,
    );
  }

  @override
  void dispose() {
    // TODO: Cleanup video resources
    super.dispose();
  }
}
