// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';

import '../../../../core/services/agora_service.dart';
import '../../../../core/config/agora_config.dart';
import '../../controllers/live_stream_controller.dart';

class LiveStreamVideoView extends StatefulWidget {
  final bool isHost;

  const LiveStreamVideoView({
    super.key,
    this.isHost = true,
  });

  @override
  State<LiveStreamVideoView> createState() => _LiveStreamVideoViewState();
}

class _LiveStreamVideoViewState extends State<LiveStreamVideoView> {
  late final LiveStreamController _liveStreamController;
  late final AgoraService _agoraService;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _liveStreamController = GetIt.I.get<LiveStreamController>();
    _agoraService = AgoraService.instance;
    _initializeVideo();
  }

  void _initializeVideo() async {
    if (!AgoraConfig.isValidAppId()) {
      print(
          'Warning: Agora App ID not configured. Please set your App ID in AgoraConfig.');
      return;
    }

    try {
      final success = await _agoraService.initialize(appId: AgoraConfig.appId);
      if (success && mounted) {
        setState(() {
          _isInitialized = true;
        });

        // Join channel if we have stream data
        final currentStream =
            _liveStreamController.value.data.currentLiveStream;
        if (currentStream != null) {
          await _joinChannel(currentStream);
        }
      }
    } catch (e) {
      print('Failed to initialize Agora: $e');
    }
  }

  Future<void> _joinChannel(dynamic liveStream) async {
    try {
      // For now, use a placeholder token until backend integration is complete
      final channelName = AgoraConfig.generateChannelName(liveStream.id);

      // Generate a random UID for testing
      final uid = DateTime.now().millisecondsSinceEpoch % 100000;

      await _agoraService.joinChannel(
        channelName: channelName,
        token:
            '', // Empty token for testing (requires Agora project to allow it)
        uid: uid,
        isHost: widget.isHost,
      );
    } catch (e) {
      print('Failed to join channel: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<SLoadingState<LiveStreamState>>(
      valueListenable: _liveStreamController,
      builder: (context, state, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF1a1a1a),
                Color(0xFF2d2d2d),
                Color(0xFF1a1a1a),
              ],
            ),
          ),
          child: Stack(
            children: [
              // Video stream content
              if (_isInitialized && _agoraService.isJoined)
                // Show actual video streams
                _buildVideoStreams()
              else
                // Show placeholder while connecting
                _buildPlaceholder(),

              // Video controls overlay (for host)
              if (widget.isHost)
                Positioned(
                  bottom: 20,
                  right: 20,
                  child: Column(
                    children: [
                      // Camera flip button
                      _buildVideoControl(
                        icon: CupertinoIcons.camera_rotate,
                        onTap: _flipCamera,
                      ),
                      const SizedBox(height: 12),

                      // Camera on/off button
                      _buildVideoControl(
                        icon: CupertinoIcons.video_camera,
                        onTap: _toggleCamera,
                      ),
                      const SizedBox(height: 12),

                      // Microphone on/off button
                      _buildVideoControl(
                        icon: CupertinoIcons.mic,
                        onTap: _toggleMicrophone,
                      ),
                    ],
                  ),
                ),

              // Connection status indicator
              if (state.data.isInLiveStream)
                Positioned(
                  top: 20,
                  right: 20,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          CupertinoIcons.wifi,
                          color: Colors.white,
                          size: 12,
                        ),
                        SizedBox(width: 4),
                        Text(
                          'Connected',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildVideoControl({
    required IconData icon,
    required VoidCallback onTap,
    bool isActive = true,
  }) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onTap,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: isActive
              ? Colors.white.withOpacity(0.2)
              : Colors.red.withOpacity(0.8),
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildVideoStreams() {
    return StreamBuilder<List<int>>(
      stream: _agoraService.remoteUsersStream,
      builder: (context, snapshot) {
        final remoteUsers = snapshot.data ?? [];

        if (widget.isHost) {
          // Host view: Show local video with remote users overlay
          return Stack(
            children: [
              // Local video (host camera)
              if (_agoraService.engine != null)
                AgoraVideoView(
                  controller: VideoViewController(
                    rtcEngine: _agoraService.engine!,
                    canvas: const VideoCanvas(uid: 0),
                  ),
                ),

              // Remote users in small windows
              if (remoteUsers.isNotEmpty)
                Positioned(
                  top: 20,
                  right: 20,
                  child: SizedBox(
                    height: 120,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: remoteUsers.length,
                      itemBuilder: (context, index) {
                        return Container(
                          width: 80,
                          height: 120,
                          margin: const EdgeInsets.only(left: 8),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: AgoraVideoView(
                              controller: VideoViewController.remote(
                                rtcEngine: _agoraService.engine!,
                                canvas: VideoCanvas(uid: remoteUsers[index]),
                                connection: RtcConnection(
                                  channelId: _agoraService.currentChannel,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
            ],
          );
        } else {
          // Viewer: Show host video
          if (remoteUsers.isNotEmpty && _agoraService.engine != null) {
            return AgoraVideoView(
              controller: VideoViewController.remote(
                rtcEngine: _agoraService.engine!,
                canvas: VideoCanvas(uid: remoteUsers.first),
                connection: RtcConnection(
                  channelId: _agoraService.currentChannel,
                ),
              ),
            );
          } else {
            return _buildPlaceholder();
          }
        }
      },
    );
  }

  Widget _buildPlaceholder() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              CupertinoIcons.video_camera,
              size: 60,
              color: Colors.white54,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            widget.isHost ? 'Your Camera' : 'Live Stream',
            style: const TextStyle(
              color: Colors.white54,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.isHost
                ? _isInitialized
                    ? 'Starting camera...'
                    : 'Initializing video...'
                : _isInitialized
                    ? 'Connecting to stream...'
                    : 'Loading...',
            style: const TextStyle(
              color: Colors.white38,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _flipCamera() {
    _agoraService.switchCamera();
  }

  void _toggleCamera() {
    _agoraService.toggleCamera();
  }

  void _toggleMicrophone() {
    _agoraService.toggleMicrophone();
  }

  @override
  void dispose() {
    // Leave channel when disposing
    if (_agoraService.isJoined) {
      _agoraService.leaveChannel();
    }
    super.dispose();
  }
}
