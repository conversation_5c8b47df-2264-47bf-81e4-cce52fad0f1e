// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';

import '../../../../../core/models/live_stream/live_stream_model.dart';
import '../../../../live_stream/controllers/live_stream_controller.dart';
import '../../../../live_stream/views/start_live_view.dart';
import 'live_stream_widget.dart';

class LiveStreamSection extends StatefulWidget {
  const LiveStreamSection({super.key});

  @override
  State<LiveStreamSection> createState() => _LiveStreamSectionState();
}

class _LiveStreamSectionState extends State<LiveStreamSection> {
  late final LiveStreamController _liveStreamController;

  @override
  void initState() {
    super.initState();
    _liveStreamController = Get.put(LiveStreamController());
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<SLoadingState<LiveStreamState>>(
      valueListenable: _liveStreamController,
      builder: (context, state, child) {
        // Don't show section if no active streams and not loading
        if (!state.data.hasActiveLiveStreams &&
            !state.data.isLoadingStreams &&
            state.isSuccess) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Container(
                    width: 4,
                    height: 20,
                    decoration: BoxDecoration(
                      color: CupertinoColors.systemBlue,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    S.of(context).liveStreams,
                    style: context.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  if (state.data.isLoadingStreams)
                    const CupertinoActivityIndicator(),
                ],
              ),
            ),

            // Live streams list
            if (state.data.hasActiveLiveStreams)
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  itemCount: state.data.activeLiveStreams.length +
                      1, // +1 for create button
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      // Create live stream button
                      return SizedBox(
                        width: 280,
                        child: LiveStreamCreateWidget(
                          onTap: _onCreateLiveStream,
                          isLoading: state.isLoading,
                        ),
                      );
                    }

                    final liveStream = state.data.activeLiveStreams[index - 1];
                    return SizedBox(
                      width: 300,
                      child: LiveStreamWidget(
                        liveStream: liveStream,
                        onTap: () => _onLiveStreamTap(liveStream),
                        onJoin: () => _onJoinLiveStream(liveStream),
                      ),
                    );
                  },
                ),
              )
            else if (state.data.isLoadingStreams)
              // Loading state
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  itemCount: 3,
                  itemBuilder: (context, index) {
                    return Container(
                      width: 300,
                      margin: const EdgeInsets.symmetric(
                          horizontal: 4, vertical: 8),
                      decoration: BoxDecoration(
                        color: CupertinoColors.systemGrey6,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Center(
                        child: CupertinoActivityIndicator(),
                      ),
                    );
                  },
                ),
              )
            else
              // Create button only when no streams
              SizedBox(
                height: 80,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: LiveStreamCreateWidget(
                    onTap: _onCreateLiveStream,
                    isLoading: state.isLoading,
                  ),
                ),
              ),

            const SizedBox(height: 16),
          ],
        );
      },
    );
  }

  void _onCreateLiveStream() {
    // Navigate to create live stream screen
    context.toPage(const StartLiveView());
  }

  void _onLiveStreamTap(LiveStreamModel liveStream) {
    // Show live stream details or join directly
    _showLiveStreamOptions(liveStream);
  }

  void _onJoinLiveStream(LiveStreamModel liveStream) {
    // Join the live stream
    _liveStreamController.joinLiveStream(liveStream.id).then((_) {
      if (_liveStreamController.value.data.isInLiveStream) {
        // Navigate to live stream viewer
        Get.toNamed('/live-stream/view', arguments: {
          'liveStream': liveStream,
          'isHost': false,
        });
      }
    });
  }

  void _showLiveStreamOptions(LiveStreamModel liveStream) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => CupertinoActionSheet(
        title: Text(liveStream.title),
        message: Text(
            '${liveStream.hostData?.fullName} • ${liveStream.viewerCount} viewers'),
        actions: <CupertinoActionSheetAction>[
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              _onJoinLiveStream(liveStream);
            },
            child: Text(S.of(context).joinLiveStream),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              // Show live stream details
              _showLiveStreamDetails(liveStream);
            },
            child: Text(S.of(context).viewDetails),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          isDefaultAction: true,
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text(S.of(context).cancel),
        ),
      ),
    );
  }

  void _showLiveStreamDetails(LiveStreamModel liveStream) {
    showCupertinoDialog<void>(
      context: context,
      builder: (BuildContext context) => CupertinoAlertDialog(
        title: Text(liveStream.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 8),
            Row(
              children: [
                VCircleAvatar(
                  fullName: liveStream.hostData?.fullName ?? 'Host',
                  imageUrl: liveStream.hostData?.userImage,
                  radius: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        liveStream.hostData?.fullName ?? 'Unknown Host',
                        style: context.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '${liveStream.viewerCount} viewers • ${liveStream.durationText}',
                        style: context.textTheme.bodySmall?.copyWith(
                          color: CupertinoColors.systemGrey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (liveStream.hasDescription) ...[
              const SizedBox(height: 12),
              Text(
                liveStream.description!,
                style: context.textTheme.bodySmall,
              ),
            ],
          ],
        ),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            child: Text(S.of(context).cancel),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          CupertinoDialogAction(
            isDefaultAction: true,
            onPressed: () {
              Navigator.pop(context);
              _onJoinLiveStream(liveStream);
            },
            child: Text(S.of(context).joinLiveStream),
          ),
        ],
      ),
    );
  }
}
