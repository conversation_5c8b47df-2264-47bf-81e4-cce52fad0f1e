import 'package:super_up_core/super_up_core.dart';

class LiveStreamCommentModel {
  final String id;
  final String liveStreamId;
  final String userId;
  final SBaseUser? userData;
  final String message;
  final DateTime timestamp;
  final bool isDeleted;
  final DateTime? deletedAt;
  final String? deletedBy;
  final bool isPinned;
  final DateTime? pinnedAt;
  final String? pinnedBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  const LiveStreamCommentModel({
    required this.id,
    required this.liveStreamId,
    required this.userId,
    this.userData,
    required this.message,
    required this.timestamp,
    required this.isDeleted,
    this.deletedAt,
    this.deletedBy,
    required this.isPinned,
    this.pinnedAt,
    this.pinnedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isMe => userId == AppAuth.myId;
  bool get isVisible => !isDeleted;
  String get displayMessage => isDeleted ? '[Message deleted]' : message;
  
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  @override
  String toString() {
    return 'LiveStreamCommentModel{id: $id, userId: $userId, message: $message, isPinned: $isPinned}';
  }

  Map<String, dynamic> toMap() {
    return {
      '_id': id,
      'liveStreamId': liveStreamId,
      'userId': userId,
      'userData': userData?.toMap(),
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'isDeleted': isDeleted,
      'deletedAt': deletedAt?.toIso8601String(),
      'deletedBy': deletedBy,
      'isPinned': isPinned,
      'pinnedAt': pinnedAt?.toIso8601String(),
      'pinnedBy': pinnedBy,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory LiveStreamCommentModel.fromMap(Map<String, dynamic> map) {
    return LiveStreamCommentModel(
      id: map['_id'] as String,
      liveStreamId: map['liveStreamId'] as String,
      userId: map['userId'] is Map ? map['userId']['_id'] as String : map['userId'] as String,
      userData: map['userId'] is Map ? SBaseUser.fromMap(map['userId']) : null,
      message: map['message'] as String,
      timestamp: DateTime.parse(map['timestamp'] as String),
      isDeleted: map['isDeleted'] as bool? ?? false,
      deletedAt: map['deletedAt'] != null ? DateTime.parse(map['deletedAt'] as String) : null,
      deletedBy: map['deletedBy'] as String?,
      isPinned: map['isPinned'] as bool? ?? false,
      pinnedAt: map['pinnedAt'] != null ? DateTime.parse(map['pinnedAt'] as String) : null,
      pinnedBy: map['pinnedBy'] as String?,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  LiveStreamCommentModel copyWith({
    String? id,
    String? liveStreamId,
    String? userId,
    SBaseUser? userData,
    String? message,
    DateTime? timestamp,
    bool? isDeleted,
    DateTime? deletedAt,
    String? deletedBy,
    bool? isPinned,
    DateTime? pinnedAt,
    String? pinnedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LiveStreamCommentModel(
      id: id ?? this.id,
      liveStreamId: liveStreamId ?? this.liveStreamId,
      userId: userId ?? this.userId,
      userData: userData ?? this.userData,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      isDeleted: isDeleted ?? this.isDeleted,
      deletedAt: deletedAt ?? this.deletedAt,
      deletedBy: deletedBy ?? this.deletedBy,
      isPinned: isPinned ?? this.isPinned,
      pinnedAt: pinnedAt ?? this.pinnedAt,
      pinnedBy: pinnedBy ?? this.pinnedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LiveStreamCommentModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
