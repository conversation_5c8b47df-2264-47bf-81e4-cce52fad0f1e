import 'package:enum_to_string/enum_to_string.dart';
import 'package:super_up/app/core/utils/enums.dart';
import 'package:super_up_core/super_up_core.dart';

class LiveStreamReactionModel {
  final String id;
  final String liveStreamId;
  final String userId;
  final SBaseUser? userData;
  final LiveStreamReactionType reactionType;
  final DateTime timestamp;
  final DateTime createdAt;
  final DateTime updatedAt;

  const LiveStreamReactionModel({
    required this.id,
    required this.liveStreamId,
    required this.userId,
    this.userData,
    required this.reactionType,
    required this.timestamp,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isMe => userId == AppAuth.myId;
  
  String get reactionEmoji {
    switch (reactionType) {
      case LiveStreamReactionType.like:
        return '👍';
      case LiveStreamReactionType.heart:
        return '❤️';
      case LiveStreamReactionType.laugh:
        return '😂';
      case LiveStreamReactionType.wow:
        return '😮';
      case LiveStreamReactionType.sad:
        return '😢';
      case LiveStreamReactionType.angry:
        return '😡';
    }
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  @override
  String toString() {
    return 'LiveStreamReactionModel{id: $id, userId: $userId, reactionType: $reactionType}';
  }

  Map<String, dynamic> toMap() {
    return {
      '_id': id,
      'liveStreamId': liveStreamId,
      'userId': userId,
      'userData': userData?.toMap(),
      'reactionType': reactionType.name,
      'timestamp': timestamp.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory LiveStreamReactionModel.fromMap(Map<String, dynamic> map) {
    return LiveStreamReactionModel(
      id: map['_id'] as String,
      liveStreamId: map['liveStreamId'] as String,
      userId: map['userId'] is Map ? map['userId']['_id'] as String : map['userId'] as String,
      userData: map['userId'] is Map ? SBaseUser.fromMap(map['userId']) : null,
      reactionType: EnumToString.fromString(
        LiveStreamReactionType.values,
        map['reactionType'] as String,
      ) ?? LiveStreamReactionType.like,
      timestamp: DateTime.parse(map['timestamp'] as String),
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  LiveStreamReactionModel copyWith({
    String? id,
    String? liveStreamId,
    String? userId,
    SBaseUser? userData,
    LiveStreamReactionType? reactionType,
    DateTime? timestamp,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LiveStreamReactionModel(
      id: id ?? this.id,
      liveStreamId: liveStreamId ?? this.liveStreamId,
      userId: userId ?? this.userId,
      userData: userData ?? this.userData,
      reactionType: reactionType ?? this.reactionType,
      timestamp: timestamp ?? this.timestamp,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LiveStreamReactionModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

class LiveStreamReactionStats {
  final List<ReactionTypeCount> reactions;
  final int totalReactions;

  const LiveStreamReactionStats({
    required this.reactions,
    required this.totalReactions,
  });

  int getCountForType(LiveStreamReactionType type) {
    final reaction = reactions.firstWhere(
      (r) => r.type == type,
      orElse: () => ReactionTypeCount(type: type, count: 0),
    );
    return reaction.count;
  }

  List<ReactionTypeCount> get topReactions {
    final sorted = List<ReactionTypeCount>.from(reactions);
    sorted.sort((a, b) => b.count.compareTo(a.count));
    return sorted.take(3).toList();
  }

  Map<String, dynamic> toMap() {
    return {
      'reactions': reactions.map((r) => r.toMap()).toList(),
      'totalReactions': totalReactions,
    };
  }

  factory LiveStreamReactionStats.fromMap(Map<String, dynamic> map) {
    return LiveStreamReactionStats(
      reactions: (map['reactions'] as List?)
          ?.map((r) => ReactionTypeCount.fromMap(r))
          .toList() ?? [],
      totalReactions: map['totalReactions'] as int? ?? 0,
    );
  }
}

class ReactionTypeCount {
  final LiveStreamReactionType type;
  final int count;

  const ReactionTypeCount({
    required this.type,
    required this.count,
  });

  String get emoji {
    switch (type) {
      case LiveStreamReactionType.like:
        return '👍';
      case LiveStreamReactionType.heart:
        return '❤️';
      case LiveStreamReactionType.laugh:
        return '😂';
      case LiveStreamReactionType.wow:
        return '😮';
      case LiveStreamReactionType.sad:
        return '😢';
      case LiveStreamReactionType.angry:
        return '😡';
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'count': count,
    };
  }

  factory ReactionTypeCount.fromMap(Map<String, dynamic> map) {
    return ReactionTypeCount(
      type: EnumToString.fromString(
        LiveStreamReactionType.values,
        map['type'] as String,
      ) ?? LiveStreamReactionType.like,
      count: map['count'] as int,
    );
  }
}
