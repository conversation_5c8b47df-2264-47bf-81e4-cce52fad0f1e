// Copyright 2023, the hate<PERSON><PERSON><PERSON> project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

class AgoraTokenResponse {
  final String token;
  final int uid;
  final String channelName;
  final int expirationTime;
  final String role;

  const AgoraTokenResponse({
    required this.token,
    required this.uid,
    required this.channelName,
    required this.expirationTime,
    required this.role,
  });

  factory AgoraTokenResponse.fromMap(Map<String, dynamic> map) {
    return AgoraTokenResponse(
      token: (map['token'] ?? map['rtcToken']) as String,
      uid: map['uid'] as int,
      channelName: map['channelName'] as String,
      expirationTime: (map['expirationTime'] ??
          (map['expiresAt'] != null
              ? DateTime.parse(map['expiresAt'].toString())
                      .millisecondsSinceEpoch ~/
                  1000
              : DateTime.now().add(Duration(hours: 1)).millisecondsSinceEpoch ~/
                  1000)) as int,
      role: map['role'] as String,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'token': token,
      'uid': uid,
      'channelName': channelName,
      'expirationTime': expirationTime,
      'role': role,
    };
  }

  AgoraTokenResponse copyWith({
    String? token,
    int? uid,
    String? channelName,
    int? expirationTime,
    String? role,
  }) {
    return AgoraTokenResponse(
      token: token ?? this.token,
      uid: uid ?? this.uid,
      channelName: channelName ?? this.channelName,
      expirationTime: expirationTime ?? this.expirationTime,
      role: role ?? this.role,
    );
  }

  @override
  String toString() {
    return 'AgoraTokenResponse(token: $token, uid: $uid, channelName: $channelName, expirationTime: $expirationTime, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AgoraTokenResponse &&
        other.token == token &&
        other.uid == uid &&
        other.channelName == channelName &&
        other.expirationTime == expirationTime &&
        other.role == role;
  }

  @override
  int get hashCode {
    return token.hashCode ^
        uid.hashCode ^
        channelName.hashCode ^
        expirationTime.hashCode ^
        role.hashCode;
  }

  // Helper methods
  bool get isExpired {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return now >= expirationTime;
  }

  Duration get timeUntilExpiration {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final secondsLeft = expirationTime - now;
    return Duration(seconds: secondsLeft > 0 ? secondsLeft : 0);
  }

  bool get isHost =>
      role.toLowerCase() == 'host' || role.toLowerCase() == 'broadcaster';
  bool get isViewer =>
      role.toLowerCase() == 'viewer' || role.toLowerCase() == 'audience';
}
