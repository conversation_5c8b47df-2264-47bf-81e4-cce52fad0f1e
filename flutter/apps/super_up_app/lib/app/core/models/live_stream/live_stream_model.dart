import 'package:enum_to_string/enum_to_string.dart';
import 'package:super_up/app/core/utils/enums.dart';
import 'package:super_up_core/super_up_core.dart';

class LiveStreamModel {
  final String id;
  final String hostId;
  final SBaseUser? hostData;
  final String title;
  final String? description;
  final LiveStreamPrivacy privacy;
  final List<String> allowedUsers;
  final List<String> bannedUsers;
  final LiveStreamStatus status;
  final String agoraChannelName;
  final LiveStreamPinnedMessage? pinnedMessage;
  final DateTime startTime;
  final DateTime? endTime;
  final int viewerCount;
  final LiveStreamFilters? filters;
  final int maxViewers;
  final int totalComments;
  final int totalReactions;
  final DateTime createdAt;
  final DateTime updatedAt;

  const LiveStreamModel({
    required this.id,
    required this.hostId,
    this.hostData,
    required this.title,
    this.description,
    required this.privacy,
    required this.allowedUsers,
    required this.bannedUsers,
    required this.status,
    required this.agoraChannelName,
    this.pinnedMessage,
    required this.startTime,
    this.endTime,
    required this.viewerCount,
    this.filters,
    required this.maxViewers,
    required this.totalComments,
    required this.totalReactions,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isActive => status == LiveStreamStatus.active;
  bool get isEnded => status == LiveStreamStatus.ended;
  bool get isMyStream => hostId == AppAuth.myId;
  bool get hasDescription => description != null && description!.isNotEmpty;
  bool get hasPinnedMessage => pinnedMessage != null;

  Duration get duration {
    final end = endTime ?? DateTime.now();
    return end.difference(startTime);
  }

  String get durationText {
    final dur = duration;
    final hours = dur.inHours;
    final minutes = dur.inMinutes % 60;
    final seconds = dur.inSeconds % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  @override
  String toString() {
    return 'LiveStreamModel{id: $id, hostId: $hostId, title: $title, status: $status, viewerCount: $viewerCount}';
  }

  Map<String, dynamic> toMap() {
    return {
      '_id': id,
      'hostId': hostId,
      'hostData': hostData?.toMap(),
      'title': title,
      'description': description,
      'privacy': privacy.name,
      'allowedUsers': allowedUsers,
      'bannedUsers': bannedUsers,
      'status': status.name,
      'agoraChannelName': agoraChannelName,
      'pinnedMessage': pinnedMessage?.toMap(),
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'viewerCount': viewerCount,
      'filters': filters?.toMap(),
      'maxViewers': maxViewers,
      'totalComments': totalComments,
      'totalReactions': totalReactions,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory LiveStreamModel.fromMap(Map<String, dynamic> map) {
    return LiveStreamModel(
      id: map['_id'] as String,
      hostId: map['hostId'] is Map ? map['hostId']['_id'] as String : map['hostId'] as String,
      hostData: map['hostId'] is Map ? SBaseUser.fromMap(map['hostId']) : null,
      title: map['title'] as String,
      description: map['description'] as String?,
      privacy: EnumToString.fromString(
        LiveStreamPrivacy.values,
        map['privacy'] as String,
      ) ?? LiveStreamPrivacy.public,
      allowedUsers: List<String>.from(map['allowedUsers'] ?? []),
      bannedUsers: List<String>.from(map['bannedUsers'] ?? []),
      status: EnumToString.fromString(
        LiveStreamStatus.values,
        map['status'] as String,
      ) ?? LiveStreamStatus.active,
      agoraChannelName: map['agoraChannelName'] as String,
      pinnedMessage: map['pinnedMessage'] != null 
        ? LiveStreamPinnedMessage.fromMap(map['pinnedMessage'])
        : null,
      startTime: DateTime.parse(map['startTime'] as String),
      endTime: map['endTime'] != null ? DateTime.parse(map['endTime'] as String) : null,
      viewerCount: map['viewerCount'] as int? ?? 0,
      filters: map['filters'] != null 
        ? LiveStreamFilters.fromMap(map['filters'])
        : null,
      maxViewers: map['maxViewers'] as int? ?? 0,
      totalComments: map['totalComments'] as int? ?? 0,
      totalReactions: map['totalReactions'] as int? ?? 0,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  LiveStreamModel copyWith({
    String? id,
    String? hostId,
    SBaseUser? hostData,
    String? title,
    String? description,
    LiveStreamPrivacy? privacy,
    List<String>? allowedUsers,
    List<String>? bannedUsers,
    LiveStreamStatus? status,
    String? agoraChannelName,
    LiveStreamPinnedMessage? pinnedMessage,
    DateTime? startTime,
    DateTime? endTime,
    int? viewerCount,
    LiveStreamFilters? filters,
    int? maxViewers,
    int? totalComments,
    int? totalReactions,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LiveStreamModel(
      id: id ?? this.id,
      hostId: hostId ?? this.hostId,
      hostData: hostData ?? this.hostData,
      title: title ?? this.title,
      description: description ?? this.description,
      privacy: privacy ?? this.privacy,
      allowedUsers: allowedUsers ?? this.allowedUsers,
      bannedUsers: bannedUsers ?? this.bannedUsers,
      status: status ?? this.status,
      agoraChannelName: agoraChannelName ?? this.agoraChannelName,
      pinnedMessage: pinnedMessage ?? this.pinnedMessage,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      viewerCount: viewerCount ?? this.viewerCount,
      filters: filters ?? this.filters,
      maxViewers: maxViewers ?? this.maxViewers,
      totalComments: totalComments ?? this.totalComments,
      totalReactions: totalReactions ?? this.totalReactions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class LiveStreamPinnedMessage {
  final String message;
  final DateTime pinnedAt;
  final String pinnedBy;

  const LiveStreamPinnedMessage({
    required this.message,
    required this.pinnedAt,
    required this.pinnedBy,
  });

  Map<String, dynamic> toMap() {
    return {
      'message': message,
      'pinnedAt': pinnedAt.toIso8601String(),
      'pinnedBy': pinnedBy,
    };
  }

  factory LiveStreamPinnedMessage.fromMap(Map<String, dynamic> map) {
    return LiveStreamPinnedMessage(
      message: map['message'] as String,
      pinnedAt: DateTime.parse(map['pinnedAt'] as String),
      pinnedBy: map['pinnedBy'] as String,
    );
  }
}

class LiveStreamFilters {
  final String filterType;
  final Map<String, dynamic> filterData;

  const LiveStreamFilters({
    required this.filterType,
    required this.filterData,
  });

  Map<String, dynamic> toMap() {
    return {
      'filterType': filterType,
      'filterData': filterData,
    };
  }

  factory LiveStreamFilters.fromMap(Map<String, dynamic> map) {
    return LiveStreamFilters(
      filterType: map['filterType'] as String,
      filterData: Map<String, dynamic>.from(map['filterData'] ?? {}),
    );
  }
}
