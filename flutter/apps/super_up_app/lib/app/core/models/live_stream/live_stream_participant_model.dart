import 'package:enum_to_string/enum_to_string.dart';
import 'package:super_up/app/core/utils/enums.dart';
import 'package:super_up_core/super_up_core.dart';

class LiveStreamParticipantModel {
  final String id;
  final String liveStreamId;
  final String userId;
  final SBaseUser? userData;
  final LiveStreamParticipantRole role;
  final DateTime joinedAt;
  final DateTime? leftAt;
  final bool isMuted;
  final bool isBanned;
  final int watchDuration; // in seconds
  final DateTime lastActiveAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  const LiveStreamParticipantModel({
    required this.id,
    required this.liveStreamId,
    required this.userId,
    this.userData,
    required this.role,
    required this.joinedAt,
    this.leftAt,
    required this.isMuted,
    required this.isBanned,
    required this.watchDuration,
    required this.lastActiveAt,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isHost => role == LiveStreamParticipantRole.host;
  bool get isViewer => role == LiveStreamParticipantRole.viewer;
  bool get isActive => leftAt == null;
  bool get isMe => userId == AppAuth.myId;

  Duration get watchDurationDuration => Duration(seconds: watchDuration);
  
  String get watchDurationText {
    final dur = watchDurationDuration;
    final hours = dur.inHours;
    final minutes = dur.inMinutes % 60;
    final seconds = dur.inSeconds % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  Duration get sessionDuration {
    final end = leftAt ?? DateTime.now();
    return end.difference(joinedAt);
  }

  @override
  String toString() {
    return 'LiveStreamParticipantModel{id: $id, userId: $userId, role: $role, isActive: $isActive}';
  }

  Map<String, dynamic> toMap() {
    return {
      '_id': id,
      'liveStreamId': liveStreamId,
      'userId': userId,
      'userData': userData?.toMap(),
      'role': role.name,
      'joinedAt': joinedAt.toIso8601String(),
      'leftAt': leftAt?.toIso8601String(),
      'isMuted': isMuted,
      'isBanned': isBanned,
      'watchDuration': watchDuration,
      'lastActiveAt': lastActiveAt.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory LiveStreamParticipantModel.fromMap(Map<String, dynamic> map) {
    return LiveStreamParticipantModel(
      id: map['_id'] as String,
      liveStreamId: map['liveStreamId'] as String,
      userId: map['userId'] is Map ? map['userId']['_id'] as String : map['userId'] as String,
      userData: map['userId'] is Map ? SBaseUser.fromMap(map['userId']) : null,
      role: EnumToString.fromString(
        LiveStreamParticipantRole.values,
        map['role'] as String,
      ) ?? LiveStreamParticipantRole.viewer,
      joinedAt: DateTime.parse(map['joinedAt'] as String),
      leftAt: map['leftAt'] != null ? DateTime.parse(map['leftAt'] as String) : null,
      isMuted: map['isMuted'] as bool? ?? false,
      isBanned: map['isBanned'] as bool? ?? false,
      watchDuration: map['watchDuration'] as int? ?? 0,
      lastActiveAt: DateTime.parse(map['lastActiveAt'] as String),
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  LiveStreamParticipantModel copyWith({
    String? id,
    String? liveStreamId,
    String? userId,
    SBaseUser? userData,
    LiveStreamParticipantRole? role,
    DateTime? joinedAt,
    DateTime? leftAt,
    bool? isMuted,
    bool? isBanned,
    int? watchDuration,
    DateTime? lastActiveAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LiveStreamParticipantModel(
      id: id ?? this.id,
      liveStreamId: liveStreamId ?? this.liveStreamId,
      userId: userId ?? this.userId,
      userData: userData ?? this.userData,
      role: role ?? this.role,
      joinedAt: joinedAt ?? this.joinedAt,
      leftAt: leftAt ?? this.leftAt,
      isMuted: isMuted ?? this.isMuted,
      isBanned: isBanned ?? this.isBanned,
      watchDuration: watchDuration ?? this.watchDuration,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LiveStreamParticipantModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
