import 'dart:convert';
import 'package:chopper/chopper.dart';
import 'package:super_up/app/core/utils/enums.dart';

class CreateLiveStreamDto {
  final String title;
  final String? description;
  final LiveStreamPrivacy privacy;
  final List<String>? allowedUsers;
  final LiveStreamFiltersDto? filters;

  const CreateLiveStreamDto({
    required this.title,
    this.description,
    required this.privacy,
    this.allowedUsers,
    this.filters,
  });

  List<PartValue> toListOfPartValue() {
    return [
      PartValue('title', title),
      if (description != null) PartValue('description', description!),
      PartValue('privacy', privacy.name),
      if (allowedUsers != null) PartValue('allowedUsers', jsonEncode(allowedUsers)),
      if (filters != null) PartValue('filters', jsonEncode(filters!.toMap())),
    ];
  }

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'description': description,
      'privacy': privacy.name,
      'allowedUsers': allowedUsers,
      'filters': filters?.toMap(),
    };
  }
}

class JoinLiveStreamDto {
  final String liveStreamId;

  const JoinLiveStreamDto({
    required this.liveStreamId,
  });

  Map<String, dynamic> toMap() {
    return {
      'liveStreamId': liveStreamId,
    };
  }
}

class LeaveLiveStreamDto {
  final String liveStreamId;

  const LeaveLiveStreamDto({
    required this.liveStreamId,
  });

  Map<String, dynamic> toMap() {
    return {
      'liveStreamId': liveStreamId,
    };
  }
}

class InviteUsersDto {
  final String liveStreamId;
  final List<String> userIds;

  const InviteUsersDto({
    required this.liveStreamId,
    required this.userIds,
  });

  Map<String, dynamic> toMap() {
    return {
      'liveStreamId': liveStreamId,
      'userIds': userIds,
    };
  }
}

class BanUserDto {
  final String liveStreamId;
  final String userId;

  const BanUserDto({
    required this.liveStreamId,
    required this.userId,
  });

  Map<String, dynamic> toMap() {
    return {
      'liveStreamId': liveStreamId,
      'userId': userId,
    };
  }
}

class UnbanUserDto {
  final String liveStreamId;
  final String userId;

  const UnbanUserDto({
    required this.liveStreamId,
    required this.userId,
  });

  Map<String, dynamic> toMap() {
    return {
      'liveStreamId': liveStreamId,
      'userId': userId,
    };
  }
}

class PinMessageDto {
  final String liveStreamId;
  final String message;

  const PinMessageDto({
    required this.liveStreamId,
    required this.message,
  });

  Map<String, dynamic> toMap() {
    return {
      'liveStreamId': liveStreamId,
      'message': message,
    };
  }
}

class AddCommentDto {
  final String liveStreamId;
  final String message;

  const AddCommentDto({
    required this.liveStreamId,
    required this.message,
  });

  Map<String, dynamic> toMap() {
    return {
      'liveStreamId': liveStreamId,
      'message': message,
    };
  }
}

class AddReactionDto {
  final String liveStreamId;
  final LiveStreamReactionType reactionType;

  const AddReactionDto({
    required this.liveStreamId,
    required this.reactionType,
  });

  Map<String, dynamic> toMap() {
    return {
      'liveStreamId': liveStreamId,
      'reactionType': reactionType.name,
    };
  }
}

class MuteLiveStreamDto {
  final String liveStreamId;

  const MuteLiveStreamDto({
    required this.liveStreamId,
  });

  Map<String, dynamic> toMap() {
    return {
      'liveStreamId': liveStreamId,
    };
  }
}

class LiveStreamFiltersDto {
  final String filterType;
  final Map<String, dynamic> filterData;

  const LiveStreamFiltersDto({
    required this.filterType,
    required this.filterData,
  });

  Map<String, dynamic> toMap() {
    return {
      'filterType': filterType,
      'filterData': filterData,
    };
  }

  factory LiveStreamFiltersDto.fromMap(Map<String, dynamic> map) {
    return LiveStreamFiltersDto(
      filterType: map['filterType'] as String,
      filterData: Map<String, dynamic>.from(map['filterData'] ?? {}),
    );
  }
}

class AgoraTokenResponse {
  final String channelName;
  final int uid;
  final String rtcToken;
  final String? role;
  final String? agoraRole;
  final DateTime joinedAt;
  final DateTime? expiresAt;

  const AgoraTokenResponse({
    required this.channelName,
    required this.uid,
    required this.rtcToken,
    this.role,
    this.agoraRole,
    required this.joinedAt,
    this.expiresAt,
  });

  factory AgoraTokenResponse.fromMap(Map<String, dynamic> map) {
    return AgoraTokenResponse(
      channelName: map['channelName'] as String,
      uid: map['uid'] as int,
      rtcToken: map['rtcToken'] as String,
      role: map['role'] as String?,
      agoraRole: map['agoraRole'] as String?,
      joinedAt: DateTime.parse(map['joinedAt'] as String),
      expiresAt: map['expiresAt'] != null ? DateTime.parse(map['expiresAt'] as String) : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'channelName': channelName,
      'uid': uid,
      'rtcToken': rtcToken,
      'role': role,
      'agoraRole': agoraRole,
      'joinedAt': joinedAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
    };
  }
}
