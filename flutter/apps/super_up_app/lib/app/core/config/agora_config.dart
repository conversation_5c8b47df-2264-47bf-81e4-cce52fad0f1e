// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

class AgoraConfig {
  // TODO: Replace with your actual Agora App ID
  // Get your App ID from: https://console.agora.io/
  static const String appId = 'YOUR_AGORA_APP_ID';
  
  // Channel settings
  static const String channelPrefix = 'live_stream_';
  
  // Video settings
  static const int defaultVideoWidth = 640;
  static const int defaultVideoHeight = 480;
  static const int defaultFrameRate = 15;
  static const int defaultBitrate = 400;
  
  // Audio settings
  static const int defaultAudioBitrate = 48;
  static const int defaultAudioSampleRate = 48000;
  
  // Connection settings
  static const int connectionTimeout = 10000; // 10 seconds
  static const int maxRetryAttempts = 3;
  
  // Generate channel name for live stream
  static String generateChannelName(String liveStreamId) {
    return '$channelPrefix$liveStreamId';
  }
  
  // Validate App ID
  static bool isValidAppId() {
    return appId.isNotEmpty && appId != 'YOUR_AGORA_APP_ID';
  }
}
