// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_files_api.dart';

// **************************************************************************
// ChopperGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
final class _$UserFiles<PERSON>pi extends User<PERSON>ilesApi {
  _$User<PERSON>iles<PERSON>pi([ChopperClient? client]) {
    if (client == null) return;
    this.client = client;
  }

  @override
  final Type definitionType = UserFilesApi;

  @override
  Future<Response<dynamic>> getUserFiles({
    int? page,
    int? limit,
    String? fileType,
  }) {
    final Uri $url = Uri.parse('user/files');
    final Map<String, dynamic> $params = <String, dynamic>{
      'page': page,
      'limit': limit,
      'fileType': fileType,
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      parameters: $params,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> deleteFile(String fileId) {
    final Uri $url = Uri.parse('user/files/${fileId}');
    final Request $request = Request(
      'DELETE',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> deleteMultipleFiles(Map<String, dynamic> body) {
    final Uri $url = Uri.parse('user/files');
    final $body = body;
    final Request $request = Request(
      'DELETE',
      $url,
      client.baseUrl,
      body: $body,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> cleanupOrphanedFiles() {
    final Uri $url = Uri.parse('user/files/cleanup');
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }
}
