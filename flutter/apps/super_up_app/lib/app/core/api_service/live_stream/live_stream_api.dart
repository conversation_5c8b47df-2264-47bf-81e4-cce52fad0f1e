// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:io';

import 'package:chopper/chopper.dart';
import 'package:http/http.dart' hide Response, Request;
import 'package:http/io_client.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_platform/v_platform.dart';

import '../interceptors.dart';

part 'live_stream_api.chopper.dart';

@ChopperApi(baseUrl: 'live-stream')
abstract class LiveStreamApi extends ChopperService {
  // Live Stream Management
  @POST(path: "/start")
  Future<Response> startLiveStream(@Body() Map<String, dynamic> body);

  @POST(path: "/{id}/end", optionalBody: true)
  Future<Response> endLiveStream(@Path("id") String id);

  @GET(path: "/active")
  Future<Response> getActiveLiveStreams(@QueryMap() Map<String, dynamic> query);

  @GET(path: "/{id}")
  Future<Response> getLiveStream(@Path("id") String id);

  // Participant Management
  @POST(path: "/{id}/join", optionalBody: true)
  Future<Response> joinLiveStream(@Path("id") String id);

  @POST(path: "/{id}/leave", optionalBody: true)
  Future<Response> leaveLiveStream(@Path("id") String id);

  @GET(path: "/{id}/participants")
  Future<Response> getParticipants(
    @Path("id") String id,
    @QueryMap() Map<String, dynamic> query,
  );

  // User Management
  @POST(path: "/{id}/invite")
  Future<Response> inviteUsers(
    @Path("id") String id,
    @Body() Map<String, dynamic> body,
  );

  @POST(path: "/{id}/ban")
  Future<Response> banUser(
    @Path("id") String id,
    @Body() Map<String, dynamic> body,
  );

  @POST(path: "/{id}/unban")
  Future<Response> unbanUser(
    @Path("id") String id,
    @Body() Map<String, dynamic> body,
  );

  // Message Management
  @POST(path: "/{id}/pin-message")
  Future<Response> pinMessage(
    @Path("id") String id,
    @Body() Map<String, dynamic> body,
  );

  // Comments
  @POST(path: "/{id}/comment")
  Future<Response> addComment(
    @Path("id") String id,
    @Body() Map<String, dynamic> body,
  );

  @GET(path: "/{id}/comments")
  Future<Response> getComments(
    @Path("id") String id,
    @QueryMap() Map<String, dynamic> query,
  );

  // Reactions
  @POST(path: "/{id}/react")
  Future<Response> addReaction(
    @Path("id") String id,
    @Body() Map<String, dynamic> body,
  );

  @GET(path: "/{id}/reactions")
  Future<Response> getReactions(
    @Path("id") String id,
    @QueryMap() Map<String, dynamic> query,
  );

  // Agora Token
  @GET(path: "/{id}/agora-token")
  Future<Response> getAgoraToken(@Path("id") String id);

  static LiveStreamApi create() {
    final client = ChopperClient(
      baseUrl: SConstants.sApiBaseUrl,
      client: VPlatforms.isWeb
          ? null
          : IOClient(
              HttpClient()
                ..badCertificateCallback = (cert, host, port) {
                  return true;
                },
            ),
      services: [
        _$LiveStreamApi(),
      ],
      interceptors: [
        HttpLoggingInterceptor(),
        AuthInterceptor(),
        CurlInterceptor(),
      ],
      converter: const JsonConverter(),
    );
    return _$LiveStreamApi(client);
  }
}
