// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:super_up/app/core/api_service/live_stream/live_stream_api.dart';
import 'package:super_up/app/core/models/live_stream/live_stream_model.dart';
import 'package:super_up/app/core/models/live_stream/live_stream_participant_model.dart';
import 'package:super_up/app/core/models/live_stream/live_stream_comment_model.dart';
import 'package:super_up/app/core/models/live_stream/live_stream_reaction_model.dart';
import 'package:super_up/app/core/models/live_stream/live_stream_dto.dart';
import 'package:super_up_core/super_up_core.dart';

import '../interceptors.dart';

class LiveStreamApiService {
  static LiveStreamApi? _liveStreamApi;

  LiveStreamApiService._();

  static LiveStreamApiService init() {
    _liveStreamApi = LiveStreamApi.create();
    return LiveStreamApiService._();
  }

  // Live Stream Management
  Future<Map<String, dynamic>> startLiveStream(CreateLiveStreamDto dto) async {
    final res = await _liveStreamApi!.startLiveStream(dto.toMap());
    throwIfNotSuccess(res);
    final data = extractDataFromResponse(res);
    return {
      'liveStream': LiveStreamModel.fromMap(data['liveStream']),
      'agoraToken': AgoraTokenResponse.fromMap(data['agoraToken']),
    };
  }

  Future<LiveStreamModel> endLiveStream(String liveStreamId) async {
    final res = await _liveStreamApi!.endLiveStream(liveStreamId);
    throwIfNotSuccess(res);
    return LiveStreamModel.fromMap(extractDataFromResponse(res));
  }

  Future<PaginateModel<LiveStreamModel>> getActiveLiveStreams({
    int page = 1,
    int limit = 20,
  }) async {
    final res = await _liveStreamApi!.getActiveLiveStreams({
      "page": page,
      "limit": limit,
    });
    throwIfNotSuccess(res);
    final data = extractDataFromResponse(res);
    return PaginateModel<LiveStreamModel>.fromCustomMap(
      values: (data['docs'] as List)
          .map((e) => LiveStreamModel.fromMap(e as Map<String, dynamic>))
          .toList(),
      map: data,
    );
  }

  Future<LiveStreamModel> getLiveStream(String liveStreamId) async {
    final res = await _liveStreamApi!.getLiveStream(liveStreamId);
    throwIfNotSuccess(res);
    return LiveStreamModel.fromMap(extractDataFromResponse(res));
  }

  // Participant Management
  Future<Map<String, dynamic>> joinLiveStream(String liveStreamId) async {
    final res = await _liveStreamApi!.joinLiveStream(liveStreamId);
    throwIfNotSuccess(res);
    final data = extractDataFromResponse(res);
    return {
      'participant': LiveStreamParticipantModel.fromMap(data['participant']),
      'agoraToken': AgoraTokenResponse.fromMap(data['agoraToken']),
      'liveStream': LiveStreamModel.fromMap(data['liveStream']),
    };
  }

  Future<void> leaveLiveStream(String liveStreamId) async {
    final res = await _liveStreamApi!.leaveLiveStream(liveStreamId);
    throwIfNotSuccess(res);
  }

  Future<PaginateModel<LiveStreamParticipantModel>> getParticipants(
    String liveStreamId, {
    int page = 1,
    int limit = 50,
  }) async {
    final res = await _liveStreamApi!.getParticipants(liveStreamId, {
      "page": page,
      "limit": limit,
    });
    throwIfNotSuccess(res);
    final data = extractDataFromResponse(res);
    return PaginateModel<LiveStreamParticipantModel>.fromCustomMap(
      values: (data['docs'] as List)
          .map((e) =>
              LiveStreamParticipantModel.fromMap(e as Map<String, dynamic>))
          .toList(),
      map: data,
    );
  }

  // User Management
  Future<void> inviteUsers(InviteUsersDto dto) async {
    final res = await _liveStreamApi!.inviteUsers(
      dto.liveStreamId,
      {'userIds': dto.userIds},
    );
    throwIfNotSuccess(res);
  }

  Future<void> banUser(BanUserDto dto) async {
    final res = await _liveStreamApi!.banUser(
      dto.liveStreamId,
      {'userId': dto.userId},
    );
    throwIfNotSuccess(res);
  }

  Future<void> unbanUser(UnbanUserDto dto) async {
    final res = await _liveStreamApi!.unbanUser(
      dto.liveStreamId,
      {'userId': dto.userId},
    );
    throwIfNotSuccess(res);
  }

  // Message Management
  Future<LiveStreamPinnedMessage> pinMessage(PinMessageDto dto) async {
    final res = await _liveStreamApi!.pinMessage(
      dto.liveStreamId,
      {'message': dto.message},
    );
    throwIfNotSuccess(res);
    return LiveStreamPinnedMessage.fromMap(extractDataFromResponse(res));
  }

  // Comments
  Future<LiveStreamCommentModel> addComment(AddCommentDto dto) async {
    final res = await _liveStreamApi!.addComment(
      dto.liveStreamId,
      {'message': dto.message},
    );
    throwIfNotSuccess(res);
    return LiveStreamCommentModel.fromMap(extractDataFromResponse(res));
  }

  Future<PaginateModel<LiveStreamCommentModel>> getComments(
    String liveStreamId, {
    int page = 1,
    int limit = 50,
  }) async {
    final res = await _liveStreamApi!.getComments(liveStreamId, {
      "page": page,
      "limit": limit,
    });
    throwIfNotSuccess(res);
    final data = extractDataFromResponse(res);
    return PaginateModel<LiveStreamCommentModel>.fromCustomMap(
      values: (data['docs'] as List)
          .map((e) => LiveStreamCommentModel.fromMap(e as Map<String, dynamic>))
          .toList(),
      map: data,
    );
  }

  // Reactions
  Future<LiveStreamReactionModel> addReaction(AddReactionDto dto) async {
    final res = await _liveStreamApi!.addReaction(
      dto.liveStreamId,
      {'reactionType': dto.reactionType.name},
    );
    throwIfNotSuccess(res);
    return LiveStreamReactionModel.fromMap(extractDataFromResponse(res));
  }

  Future<Map<String, dynamic>> getReactions(
    String liveStreamId, {
    int page = 1,
    int limit = 50,
  }) async {
    final res = await _liveStreamApi!.getReactions(liveStreamId, {
      "page": page,
      "limit": limit,
    });
    throwIfNotSuccess(res);
    final data = extractDataFromResponse(res);
    return {
      'reactions': PaginateModel<LiveStreamReactionModel>.fromCustomMap(
        values: (data['reactions']['docs'] as List)
            .map((e) =>
                LiveStreamReactionModel.fromMap(e as Map<String, dynamic>))
            .toList(),
        map: data['reactions'],
      ),
      'stats': LiveStreamReactionStats.fromMap(data['stats']),
    };
  }

  // Agora Token
  Future<AgoraTokenResponse> getAgoraToken(String liveStreamId) async {
    final res = await _liveStreamApi!.getAgoraToken(liveStreamId);
    throwIfNotSuccess(res);
    return AgoraTokenResponse.fromMap(extractDataFromResponse(res));
  }
}
