// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';
import 'dart:developer';

import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';

class AgoraService {
  static AgoraService? _instance;
  static AgoraService get instance => _instance ??= AgoraService._();
  
  AgoraService._();

  RtcEngine? _engine;
  bool _isInitialized = false;
  bool _isJoined = false;
  String? _currentChannel;
  int? _currentUid;
  
  // Stream controllers for events
  final StreamController<List<int>> _remoteUsersController = StreamController.broadcast();
  final StreamController<bool> _connectionStateController = StreamController.broadcast();
  final StreamController<Map<String, dynamic>> _errorController = StreamController.broadcast();

  // Getters for streams
  Stream<List<int>> get remoteUsersStream => _remoteUsersController.stream;
  Stream<bool> get connectionStateStream => _connectionStateController.stream;
  Stream<Map<String, dynamic>> get errorStream => _errorController.stream;

  // Current state
  List<int> _remoteUsers = [];
  bool _isCameraEnabled = true;
  bool _isMicrophoneEnabled = true;
  bool _isFrontCamera = true;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isJoined => _isJoined;
  String? get currentChannel => _currentChannel;
  int? get currentUid => _currentUid;
  List<int> get remoteUsers => List.unmodifiable(_remoteUsers);
  bool get isCameraEnabled => _isCameraEnabled;
  bool get isMicrophoneEnabled => _isMicrophoneEnabled;
  bool get isFrontCamera => _isFrontCamera;

  /// Initialize Agora SDK
  Future<bool> initialize({required String appId}) async {
    try {
      if (_isInitialized) {
        log('Agora already initialized');
        return true;
      }

      // Request permissions
      await _requestPermissions();

      // Create RTC engine
      _engine = createAgoraRtcEngine();
      await _engine!.initialize(RtcEngineContext(
        appId: appId,
        channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
      ));

      // Set up event handlers
      _setupEventHandlers();

      // Enable video
      await _engine!.enableVideo();
      await _engine!.enableAudio();

      // Set client role to broadcaster (for hosts)
      await _engine!.setClientRole(role: ClientRoleType.clientRoleBroadcaster);

      _isInitialized = true;
      log('Agora SDK initialized successfully');
      return true;
    } catch (e) {
      log('Failed to initialize Agora SDK: $e');
      _errorController.add({
        'type': 'initialization_error',
        'message': 'Failed to initialize video streaming: $e',
      });
      return false;
    }
  }

  /// Join a live stream channel
  Future<bool> joinChannel({
    required String channelName,
    required String token,
    required int uid,
    bool isHost = false,
  }) async {
    try {
      if (!_isInitialized) {
        throw Exception('Agora SDK not initialized');
      }

      if (_isJoined) {
        await leaveChannel();
      }

      // Set client role
      await _engine!.setClientRole(
        role: isHost 
            ? ClientRoleType.clientRoleBroadcaster 
            : ClientRoleType.clientRoleAudience,
      );

      // Join channel
      await _engine!.joinChannel(
        token: token,
        channelId: channelName,
        uid: uid,
        options: const ChannelMediaOptions(
          clientRoleType: ClientRoleType.clientRoleBroadcaster,
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
        ),
      );

      _currentChannel = channelName;
      _currentUid = uid;
      _isJoined = true;

      log('Joined channel: $channelName with UID: $uid');
      return true;
    } catch (e) {
      log('Failed to join channel: $e');
      _errorController.add({
        'type': 'join_error',
        'message': 'Failed to join live stream: $e',
      });
      return false;
    }
  }

  /// Leave the current channel
  Future<void> leaveChannel() async {
    try {
      if (_engine != null && _isJoined) {
        await _engine!.leaveChannel();
        _isJoined = false;
        _currentChannel = null;
        _currentUid = null;
        _remoteUsers.clear();
        _remoteUsersController.add(_remoteUsers);
        log('Left channel successfully');
      }
    } catch (e) {
      log('Failed to leave channel: $e');
    }
  }

  /// Toggle camera on/off
  Future<void> toggleCamera() async {
    try {
      if (_engine != null) {
        _isCameraEnabled = !_isCameraEnabled;
        await _engine!.enableLocalVideo(_isCameraEnabled);
        log('Camera ${_isCameraEnabled ? 'enabled' : 'disabled'}');
      }
    } catch (e) {
      log('Failed to toggle camera: $e');
    }
  }

  /// Toggle microphone on/off
  Future<void> toggleMicrophone() async {
    try {
      if (_engine != null) {
        _isMicrophoneEnabled = !_isMicrophoneEnabled;
        await _engine!.enableLocalAudio(_isMicrophoneEnabled);
        log('Microphone ${_isMicrophoneEnabled ? 'enabled' : 'disabled'}');
      }
    } catch (e) {
      log('Failed to toggle microphone: $e');
    }
  }

  /// Switch camera (front/back)
  Future<void> switchCamera() async {
    try {
      if (_engine != null) {
        await _engine!.switchCamera();
        _isFrontCamera = !_isFrontCamera;
        log('Switched to ${_isFrontCamera ? 'front' : 'back'} camera');
      }
    } catch (e) {
      log('Failed to switch camera: $e');
    }
  }

  /// Get RTC engine for custom operations
  RtcEngine? get engine => _engine;

  /// Setup event handlers
  void _setupEventHandlers() {
    _engine!.registerEventHandler(
      RtcEngineEventHandler(
        onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
          log('Local user joined channel: ${connection.channelId}');
          _connectionStateController.add(true);
        },
        onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
          log('Remote user joined: $remoteUid');
          _remoteUsers.add(remoteUid);
          _remoteUsersController.add(_remoteUsers);
        },
        onUserOffline: (RtcConnection connection, int remoteUid, UserOfflineReasonType reason) {
          log('Remote user left: $remoteUid');
          _remoteUsers.remove(remoteUid);
          _remoteUsersController.add(_remoteUsers);
        },
        onLeaveChannel: (RtcConnection connection, RtcStats stats) {
          log('Left channel: ${connection.channelId}');
          _connectionStateController.add(false);
        },
        onError: (ErrorCodeType err, String msg) {
          log('Agora error: $err - $msg');
          _errorController.add({
            'type': 'agora_error',
            'code': err.toString(),
            'message': msg,
          });
        },
        onConnectionStateChanged: (RtcConnection connection, ConnectionStateType state, ConnectionChangedReasonType reason) {
          log('Connection state changed: $state');
          _connectionStateController.add(state == ConnectionStateType.connectionStateConnected);
        },
      ),
    );
  }

  /// Request necessary permissions
  Future<void> _requestPermissions() async {
    await [
      Permission.microphone,
      Permission.camera,
    ].request();
  }

  /// Dispose and cleanup
  Future<void> dispose() async {
    try {
      if (_isJoined) {
        await leaveChannel();
      }
      
      await _engine?.release();
      _engine = null;
      _isInitialized = false;
      
      await _remoteUsersController.close();
      await _connectionStateController.close();
      await _errorController.close();
      
      log('Agora service disposed');
    } catch (e) {
      log('Error disposing Agora service: $e');
    }
  }
}
